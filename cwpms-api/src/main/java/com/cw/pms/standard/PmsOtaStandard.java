package com.cw.pms.standard;

import com.cw.pms.request.*;
import com.cw.pms.request.crsv1.*;
import com.cw.pms.response.*;
import com.cw.pms.response.crsv1.*;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/2/18 23:10
 **/
public interface PmsOtaStandard {
    // Query APIs

    /**
     * URL: /query/area
     */
    CwAreaQueryRes queryArea(CwAreaQueryReq req);

    /**
     * URL: /query/blocksku
     */
    CwBlockAllotmentQueryRes queryBlockAllotment(CwBlockAllotmentQueryReq req);

    /**
     * URL: /query/channels
     */
    CwChannelQueryRes queryChannel(CwChannelQueryReq req);

    /**
     * URL: /query/country
     */
    CwCountryCodeRes queryCountryCode(CwCountryCodeReq req);

    /**
     * URL: /query/deptcode
     */
    CwDepartmentQueryRes queryDepartment(CwDepartmentQueryReq req);

    /**
     * URL: /query/folios
     */
    CwAccountQueryRes queryAccount(CwAccountQueryReq req);

    /**
     * URL: /query/hotelcode
     */
    CwHotelCodeQueryRes queryHotelCode(CwHotelCodeQueryReq req);

    /**
     * URL: /query/idtype
     */
    CwIDTypeQueryRes queryIDType(CwIDTypeQueryReq req);

    /**
     * URL: /query/payment
     */
    CwPaymentQueryRes queryPayment(CwPaymentQueryReq req);

    /**
     * URL: /query/restype
     */
    CwResTypeQueryRes queryResType(CwResTypeQueryReq req);

    /**
     * URL: /query/roomtype
     */
    CwRoomTypeRes queryRoomType(CwRoomTypeReq req);

    /**
     * URL: /query/sku
     */
    CwRoomInventoryRes queryRoomInventory(CwRoomInventoryReq req);

    /**
     * URL: /query/sources
     */
    CwMarketQueryRes queryMarket(CwMarketQueryReq req);

    /**
     * URL: /query/sources
     */
    CwSourceQueryRes querySource(CwSourceQueryReq req);

    /**
     * URL: /query/receivable-account
     */
    CwQueryArAccountRes queryArAccount(CwQueryArAccountReq req);

    /**
     * URL: /query/room-order
     */
    CwQueryRoomOrderRes queryRoomOrder(CwQueryRoomOrderReq req);

    // Block APIs

    /**
     * URL: /block/savegrid
     */
    CwBlockAllotmentRes allotmentBlock(CwBlockAllotmentReq req);

    /**
     * URL: /block/saveblock
     */
    CwBlockRes saveBlock(CwBlockReq req);

    // Order APIs

    /**
     * URL: /order/cancel
     */
    CwCancelRoomRes cancelRoom(CwCancelRoomReq req);

    /**
     * URL: /order/save
     */
    CwSaveRoomRes saveRoom(CwSaveRoomReq req);

    /**
     * URL: /order/savecolrs
     */
    CwColRsRes saveColReservation(CwColRsReq req);

    /**
     * URL: /order/cancelcolrs
     */
    CwPmsCommonRes cancelReservation(CwCancelColRsReq req);


    /**
     * URL: /order/ar-pay
     */
    CwPmsCommonRes arpay(CwOrderArPayReq req);

    /**
     * URL: /order/room-consume
     */
    CwOrderConsumptionRes roomConsumption(CwOrderConsumptionReq req);


    /**
     * URL: /order/paycol
     */
    CwColPayRes orderPay(CwColPayReq req);

    /**
     * URL: /order/cancel-charge
     */
    CwPmsCommonRes cancelCharge(CwCancelChargeReq req);

    CwQueryColRes queryColRs(CwQueryColReq req);

    CwCheckInRes checkin(CwCheckInReq req);

    CwQueryCheckinOrderRes queryCheckinOrder(CwQueryCheckinOrderReq req);

    CwQueryHotelInfoRes queryHotelInfo(CwQueryHotelInfoReq req);


    CwCrsColRsSaveV1Res saveCrsColRsV1(CwCrsColRsSaveV1Req req);

    CwCrsRoomSaveV1Res saveCrsRoomRsV1(CwCrsRoomRsSaveV1Req req);

    CwCrsColPayV1Res crsColPayV1(CwCrsColPayV1Req req);

    /**
     * 取消综合预订
     * URL: /pmsopen/v1/cwcrs/cancelcol
     */
    CwCrsColCancelV1Res cancelCrsColV1(CwCrsColCancelV1Req req);

    /**
     * 取消客房预订
     * URL: /pmsopen/v1/cwcrs/cancelroom
     */
    CwCrsRoomCancelV1Res cancelCrsRoomV1(CwCrsRoomCancelV1Req req);

}
