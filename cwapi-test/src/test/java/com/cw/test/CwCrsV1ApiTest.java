package com.cw.test;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.cw.common.client.CwApiClient;
import com.cw.pms.model.GuestInfo;
import com.cw.pms.model.StayDateRange;
import com.cw.pms.request.crsv1.*;
import com.cw.pms.response.CwColPayRes;
import com.cw.pms.response.crsv1.CwCrsColCancelV1Res;
import com.cw.pms.response.crsv1.CwCrsColRsSaveV1Res;
import com.cw.pms.response.crsv1.CwCrsRoomCancelV1Res;
import com.cw.pms.response.crsv1.CwCrsRoomSaveV1Res;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * CrsV1 酒店系统订单测试用例
 * 测试流程：创建主单 -> 追加客房预订 -> 编辑客房预订 -> 发送预付款请求 -> 取消预付款请求 -> 取消客房预订 -> 取消综合预订
 *
 * <AUTHOR>
 */
public class CwCrsV1ApiTest {

    private CwApiClient client;
    private Date arrDate;  // 到店日期
    private Date deptDate; // 离店日期

    // 测试数据
    private String booker = "张三";
    private String phone = "13800138000";
    private String roomType = "KYSCF";
    private String rateCode = "SKJ";
    private String channelCode = "OTA";
    private String market = "LEISURE";
    private String source = "ONLINE";

    // 测试流程中的订单信息
    private String crsColId;           // 综合订单ID
    private String pmsColId;           // PMS综合订单ID
    private String crsRoomOrderId;     // CRS客房订单ID
    private String pmsRoomOrderId;     // PMS客房订单ID
    private String paymentAccountId;   // 支付账户ID

    @Before
    public void setup() {
        // 初始化API客户端
        client = new CwApiClient(
                "http://121.43.50.247:9530", // 测试服务器地址
                "002",                        // 测试应用ID
                "5bcaqh19ks128q5auw1lo23v6fi7cjht", // 测试私钥
                "RSA2"                        // 签名方式
        );

        // 设置到店离店时间（今天到店，明天离店）
        arrDate = new Date();
        deptDate = DateUtil.offsetDay(arrDate, 1);

        // 生成唯一的订单ID
        crsColId = "CRS_COL_" + RandomUtil.randomNumbers(10);
        crsRoomOrderId = "CRS_ROOM_" + RandomUtil.randomNumbers(10);

        System.out.println("=== 测试初始化完成 ===");
        System.out.println("到店日期: " + DateUtil.formatDate(arrDate));
        System.out.println("离店日期: " + DateUtil.formatDate(deptDate));
        System.out.println("综合订单ID: " + crsColId);
        System.out.println("客房订单ID: " + crsRoomOrderId);
    }

    /**
     * 完整的酒店订单测试流程
     * 按照指定顺序执行所有步骤
     */
    @Test
    public void testCompleteHotelOrderFlow() throws Exception {
        System.out.println("\n========== 开始完整酒店订单测试流程 ==========");

        // 步骤1：创建主单
        step1_createMainOrder();

        // 步骤2：追加客房预订
        step2_addRoomReservation();

        // 步骤3：编辑客房预订
        step3_editRoomReservation();

        // 步骤4：发送预付款请求
        step4_sendPrepaymentRequest();

        // 步骤5：取消预付款请求
        step5_cancelPrepaymentRequest();

        // 步骤6：取消客房预订
        step6_cancelRoomReservation();

        // 步骤7：取消综合预订
        step7_cancelColReservation();

        System.out.println("\n========== 完整酒店订单测试流程结束 ==========");
    }

    /**
     * 步骤1：创建主单 - 使用CwCrsColRsSaveV1Req
     */
    private void step1_createMainOrder() throws Exception {
        System.out.println("\n--- 步骤1：创建主单 ---");

        CwCrsColRsSaveV1Req req = new CwCrsColRsSaveV1Req();

        // 基本信息
        req.setBooker(booker);
        req.setPhone(phone);
        req.setSource(source);
        req.setResType("NORMAL");
        req.setChannelCode(channelCode);
        req.setMarket(market);
        req.setCrsColId(crsColId);
        req.setPayment("CASH");
        req.setStatus("CONFIRMED");
        req.setArrDate(arrDate);
        req.setDeptDate(deptDate);
        req.setRemark("测试创建主单");
        req.setOtaOrderId("OTA_" + RandomUtil.randomNumbers(8));
        req.setTotalAmount(new BigDecimal("500.00"));

        // 客人信息
        GuestInfo guestInfo = new GuestInfo();
        guestInfo.setName(booker);
        guestInfo.setGender("M");
        guestInfo.setMobile(phone);
        guestInfo.setIdType("ID");
        guestInfo.setIdNo("110101199001011234");
        guestInfo.setNationality("CN");
        guestInfo.setArea("北京");
        guestInfo.setAddress("北京市朝阳区测试地址");
        guestInfo.setEmail("<EMAIL>");
        guestInfo.setRemark("测试客人");
        req.setGuestInfo(guestInfo);

        // 客房预订信息
        List<CwCrsColRsSaveV1Req.Room> rooms = new ArrayList<>();
        CwCrsColRsSaveV1Req.Room room = new CwCrsColRsSaveV1Req.Room();

        // 入住日期范围
        StayDateRange stayDateRange = new StayDateRange();
        stayDateRange.setStartDate(arrDate);
        stayDateRange.setEndDate(deptDate);
        room.setStayDateRange(stayDateRange);

        room.setCrsRoomOrderId(crsRoomOrderId);
        room.setChannel(channelCode);
        room.setNumberOfRooms(1);
        room.setMarket(market);
        room.setSource(source);
        room.setRateCode(rateCode);
        room.setCommon("请安排高楼层房间");
        room.setChildren(0);
        room.setAdults(2);
        room.setSpecialCommon("无烟房");
        room.setRateVisible(true);
        room.setRoomType(roomType);

        // 房价计划
        List<CwCrsColRsSaveV1Req.Room.RatePlanBean> ratePlans = new ArrayList<>();
        CwCrsColRsSaveV1Req.Room.RatePlanBean ratePlan = new CwCrsColRsSaveV1Req.Room.RatePlanBean();
        ratePlan.setDate(arrDate);
        ratePlan.setAmount(new BigDecimal("500.00"));
        ratePlans.add(ratePlan);
        room.setRatePlan(ratePlans);

        rooms.add(room);
        req.setRooms(rooms);

        // 执行请求
        CwCrsColRsSaveV1Res response = client.execute(req);
        System.out.println("创建主单响应: " + JSON.toJSONString(response, true));

        // 保存返回的订单信息
        if (response.isSuccess() && response.getData() != null &&
                response.getData().getResults() != null && !response.getData().getResults().isEmpty()) {

            for (CwCrsColRsSaveV1Res.ProcessResult result : response.getData().getResults()) {
                if ("COLLIGATE".equals(result.getType())) {
                    pmsColId = result.getPmsno();
                    System.out.println("获取到PMS综合订单ID: " + pmsColId);
                } else if ("RESERVATION".equals(result.getType())) {
                    pmsRoomOrderId = result.getPmsno();
                    System.out.println("获取到PMS客房订单ID: " + pmsRoomOrderId);
                }
            }
        }

        System.out.println("步骤1完成 - 主单创建成功");
    }

    /**
     * 步骤2：追加客房预订 - 使用CwCrsRoomRsSaveV1Req
     */
    private void step2_addRoomReservation() throws Exception {
        System.out.println("\n--- 步骤2：追加客房预订 ---");

        CwCrsRoomRsSaveV1Req req = new CwCrsRoomRsSaveV1Req();

        // 基本信息
        req.setAdults(1);
        req.setBooker(booker);
        req.setBookPhone(phone);
        req.setChannel(channelCode);
        req.setChildren(0);
        req.setMarket(market);
        req.setNumberOfRooms(1);
        req.setOtaOrderId("OTA_ADD_" + RandomUtil.randomNumbers(8));
        req.setCrsColId(crsColId);
        req.setPmsColId(pmsColId);
        req.setCrsRoomOrderId("CRS_ROOM_ADD_" + RandomUtil.randomNumbers(10));
        req.setRateCode(rateCode);
        req.setRemark("追加客房预订");
        req.setResType("NORMAL");
        req.setRoomType("MYDCF"); // 使用不同房型
        req.setSource(source);
        req.setStatus("CONFIRMED");
        req.setCommon("追加预订，请安排相邻房间");
        req.setSpecialCommon("需要加床");
        req.setRateVisible(true);

        // 客人信息
        GuestInfo guestInfo = new GuestInfo();
        guestInfo.setName("李四");
        guestInfo.setGender("F");
        guestInfo.setMobile("13900139000");
        guestInfo.setIdType("ID");
        guestInfo.setIdNo("110101199002021234");
        guestInfo.setNationality("CN");
        req.setGuestInfo(guestInfo);

        // 入住日期范围
        StayDateRange stayDateRange = new StayDateRange();
        stayDateRange.setStartDate(arrDate);
        stayDateRange.setEndDate(deptDate);
        req.setStayDateRange(stayDateRange);

        // 房价计划
        List<CwCrsRoomRsSaveV1Req.RatePlanDetail> ratePlans = new ArrayList<>();
        CwCrsRoomRsSaveV1Req.RatePlanDetail ratePlan = new CwCrsRoomRsSaveV1Req.RatePlanDetail();
        ratePlan.setDate(arrDate);
        ratePlan.setAmount(new BigDecimal("400.00"));
        ratePlans.add(ratePlan);
        req.setRatePlan(ratePlans);

        // 执行请求
        CwCrsRoomSaveV1Res response = client.execute(req);
        System.out.println("追加客房预订响应: " + JSON.toJSONString(response, true));

        System.out.println("步骤2完成 - 客房预订追加成功");
    }

    /**
     * 步骤3：编辑客房预订 - 使用CwCrsRoomRsSaveV1Req
     */
    private void step3_editRoomReservation() throws Exception {
        System.out.println("\n--- 步骤3：编辑客房预订 ---");

        CwCrsRoomRsSaveV1Req req = new CwCrsRoomRsSaveV1Req();

        // 使用已有的订单信息进行编辑
        req.setAdults(2);
        req.setBooker(booker);
        req.setBookPhone(phone);
        req.setChannel(channelCode);
        req.setChildren(1); // 修改儿童数
        req.setMarket(market);
        req.setNumberOfRooms(1);
        req.setCrsColId(crsColId);
        req.setPmsColId(pmsColId);
        req.setCrsRoomOrderId(crsRoomOrderId);
        req.setPmsRoomOrderId(pmsRoomOrderId);
        req.setRateCode(rateCode);
        req.setRemark("编辑客房预订 - 增加儿童");
        req.setResType("NORMAL");
        req.setRoomType(roomType);
        req.setSource(source);
        req.setStatus("CONFIRMED");
        req.setCommon("编辑预订，增加一名儿童，需要加床");
        req.setSpecialCommon("儿童床，无烟房");
        req.setRateVisible(true);

        // 客人信息（更新）
        GuestInfo guestInfo = new GuestInfo();
        guestInfo.setName(booker);
        guestInfo.setGender("M");
        guestInfo.setMobile(phone);
        guestInfo.setIdType("ID");
        guestInfo.setIdNo("110101199001011234");
        guestInfo.setNationality("CN");
        guestInfo.setRemark("编辑后的客人信息，增加儿童");
        req.setGuestInfo(guestInfo);

        // 入住日期范围
        StayDateRange stayDateRange = new StayDateRange();
        stayDateRange.setStartDate(arrDate);
        stayDateRange.setEndDate(deptDate);
        req.setStayDateRange(stayDateRange);

        // 更新房价计划（因为增加了儿童，可能需要调整价格）
        List<CwCrsRoomRsSaveV1Req.RatePlanDetail> ratePlans = new ArrayList<>();
        CwCrsRoomRsSaveV1Req.RatePlanDetail ratePlan = new CwCrsRoomRsSaveV1Req.RatePlanDetail();
        ratePlan.setDate(arrDate);
        ratePlan.setAmount(new BigDecimal("550.00")); // 增加儿童后的价格
        ratePlans.add(ratePlan);
        req.setRatePlan(ratePlans);

        // 执行请求
        CwCrsRoomSaveV1Res response = client.execute(req);
        System.out.println("编辑客房预订响应: " + JSON.toJSONString(response, true));

        System.out.println("步骤3完成 - 客房预订编辑成功");
    }

    /**
     * 步骤4：发送预付款请求 - 使用CwCrsColPayV1Req
     */
    private void step4_sendPrepaymentRequest() throws Exception {
        System.out.println("\n--- 步骤4：发送预付款请求 ---");

        CwCrsColPayV1Req req = new CwCrsColPayV1Req();

        // 支付信息
        req.setAmount(new BigDecimal("300.00")); // 预付款金额
        req.setDeptCode("CASH"); // 支付方式代码
        req.setSerialNo("PAY_" + System.currentTimeMillis()); // 外部支付流水号
        req.setPmsRoomOrderId(pmsRoomOrderId); // 客房订单号
        req.setPmsColOrderId(pmsColId); // 综合订单号

        // 执行请求
        CwColPayRes response = client.execute(req);
        System.out.println("发送预付款请求响应: " + JSON.toJSONString(response, true));

        // 保存支付账户ID用于后续取消操作
        if (response.isSuccess() && response.getData() != null) {
            paymentAccountId = response.getData().getPmsAccountId();
            System.out.println("获取到支付账户ID: " + paymentAccountId);
        }

        System.out.println("步骤4完成 - 预付款请求发送成功");
    }

    /**
     * 步骤5：取消预付款请求 - 使用CwCrsColPayV1Req（退款）
     */
    private void step5_cancelPrepaymentRequest() throws Exception {
        System.out.println("\n--- 步骤5：取消预付款请求 ---");

        CwCrsColPayV1Req req = new CwCrsColPayV1Req();

        // 退款信息
        req.setAmount(new BigDecimal("-300.00")); // 负数表示退款
        req.setDeptCode("CASH"); // 支付方式代码
        req.setSerialNo("REFUND_" + System.currentTimeMillis()); // 外部退款流水号
        req.setAccid(paymentAccountId); // PMS支付流水号（用于退款）
        req.setPmsRoomOrderId(pmsRoomOrderId); // 客房订单号
        req.setPmsColOrderId(pmsColId); // 综合订单号

        // 执行请求
        CwColPayRes response = client.execute(req);
        System.out.println("取消预付款请求响应: " + JSON.toJSONString(response, true));

        System.out.println("步骤5完成 - 预付款取消成功");
    }

    /**
     * 步骤6：取消客房预订 - 使用CwCrsRoomCancelV1Req
     */
    private void step6_cancelRoomReservation() throws Exception {
        System.out.println("\n--- 步骤6：取消客房预订 ---");

        CwCrsRoomCancelV1Req req = new CwCrsRoomCancelV1Req();

        // 取消信息
        req.setPmsRoomOrderId(pmsRoomOrderId); // 使用PMS客房订单号
        req.setCancelReason("GUEST_REQUEST"); // 客人要求取消
        req.setOperator("测试操作员");
        req.setRemark("测试取消客房预订");

        // 执行请求
        CwCrsRoomCancelV1Res response = client.execute(req);
        System.out.println("取消客房预订响应: " + JSON.toJSONString(response, true));

        System.out.println("步骤6完成 - 客房预订取消成功");
    }

    /**
     * 步骤7：取消综合预订 - 使用CwCrsColCancelV1Req
     */
    private void step7_cancelColReservation() throws Exception {
        System.out.println("\n--- 步骤7：取消综合预订 ---");

        CwCrsColCancelV1Req req = new CwCrsColCancelV1Req();

        // 取消信息
        req.setPmsColId(pmsColId); // 使用PMS综合订单号
        req.setCancelReason("GUEST_REQUEST"); // 客人要求取消
        req.setOperator("测试操作员");
        req.setRemark("测试取消综合预订");

        // 执行请求
        CwCrsColCancelV1Res response = client.execute(req);
        System.out.println("取消综合预订响应: " + JSON.toJSONString(response, true));

        System.out.println("步骤7完成 - 综合预订取消成功");
    }

    /**
     * 单独测试：创建主单
     */
    @Test
    public void test1_CreateMainOrder() throws Exception {
        step1_createMainOrder();
    }

    /**
     * 单独测试：追加客房预订
     */
    @Test
    public void test2_AddRoomReservation() throws Exception {
        // 需要先创建主单
        step1_createMainOrder();
        step2_addRoomReservation();
    }

    /**
     * 单独测试：编辑客房预订
     */
    @Test
    public void test3_EditRoomReservation() throws Exception {
        // 需要先创建主单
        step1_createMainOrder();
        step3_editRoomReservation();
    }

    /**
     * 单独测试：发送预付款请求
     */
    @Test
    public void test4_SendPrepaymentRequest() throws Exception {
        // 需要先创建主单
        step1_createMainOrder();
        step4_sendPrepaymentRequest();
    }

    /**
     * 单独测试：取消预付款请求
     */
    @Test
    public void test5_CancelPrepaymentRequest() throws Exception {
        // 需要先创建主单和发送预付款
        step1_createMainOrder();
        step4_sendPrepaymentRequest();
        step5_cancelPrepaymentRequest();
    }

    /**
     * 单独测试：取消客房预订
     */
    @Test
    public void test6_CancelRoomReservation() throws Exception {
        // 需要先创建主单
        step1_createMainOrder();
        step6_cancelRoomReservation();
    }

    /**
     * 单独测试：取消综合预订
     */
    @Test
    public void test7_CancelColReservation() throws Exception {
        // 需要先创建主单
        step1_createMainOrder();
        step7_cancelColReservation();
    }

    /**
     * 测试数据验证方法
     */
    private void validateTestData() {
        System.out.println("\n=== 测试数据验证 ===");
        System.out.println("预订人: " + booker);
        System.out.println("联系电话: " + phone);
        System.out.println("房型: " + roomType);
        System.out.println("房价代码: " + rateCode);
        System.out.println("渠道代码: " + channelCode);
        System.out.println("市场代码: " + market);
        System.out.println("客源代码: " + source);
        System.out.println("到店日期: " + DateUtil.formatDate(arrDate));
        System.out.println("离店日期: " + DateUtil.formatDate(deptDate));
        System.out.println("综合订单ID: " + crsColId);
        System.out.println("客房订单ID: " + crsRoomOrderId);

        if (pmsColId != null) {
            System.out.println("PMS综合订单ID: " + pmsColId);
        }
        if (pmsRoomOrderId != null) {
            System.out.println("PMS客房订单ID: " + pmsRoomOrderId);
        }
        if (paymentAccountId != null) {
            System.out.println("支付账户ID: " + paymentAccountId);
        }
    }

    /**
     * 测试数据验证
     */
    @Test
    public void testValidateData() {
        validateTestData();
    }
}
