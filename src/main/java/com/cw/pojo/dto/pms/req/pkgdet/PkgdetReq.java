package com.cw.pojo.dto.pms.req.pkgdet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 包价详情请求实体
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@ApiModel(description = "包价详情信息请求对象")
public class PkgdetReq implements Serializable {

    @ApiModelProperty(value = "主键ID（新增时为空）", name = "id", example = "1")
    private Long id;

    /**
     * 房价代码
     */
    @ApiModelProperty(value = "房价代码", example = "PKG001")
    private String rateCode;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间 yyyy-MM-dd", example = "2024-08-01")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间 yyyy-MM-dd", example = "2024-08-31")
    private Date endTime;

    /**
     * 房型代码列表（支持多个房型）
     */
    @ApiModelProperty(value = "房型代码列表", example = "STD, SUP, DLX")
    private String roomType;

    /**
     * 包价代码
     */
    @ApiModelProperty(value = "包价代码，多个以逗号分隔", example = "3|BRF,1|WC,2|TK")
    private String includeCode;
}
