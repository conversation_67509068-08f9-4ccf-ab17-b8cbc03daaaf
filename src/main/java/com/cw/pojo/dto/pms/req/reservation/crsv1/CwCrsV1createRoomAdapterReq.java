package com.cw.pojo.dto.pms.req.reservation.crsv1;

import com.cw.entity.Colrs;
import com.cw.entity.Profile;
import com.cw.entity.Reservation;
import com.cw.pms.request.crsv1.CwCrsRoomRsSaveV1Req;
import com.cw.pojo.common.core.StdOrderRequest;
import com.cw.pojo.dto.pms.req.reservation.UpdRsParamForm;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/19 15:18
 **/
@Data
@ApiModel(description = "CRS1.0 创建单个客房订单请求")
public class CwCrsV1createRoomAdapterReq implements StdOrderRequest {
    private final CwCrsRoomRsSaveV1Req req;
    private final String hotelId;
    private final Colrs colrs;

    public CwCrsV1createRoomAdapterReq(CwCrsRoomRsSaveV1Req req, String hotelId, Colrs colrs) {
        this.req = req;
        this.hotelId = hotelId;
        this.colrs = colrs;
    }

    @Override
    public Colrs getColrs() {
        return colrs;
    }

    @Override
    public List<Reservation> getRoomRss() {
        List<Reservation> reservations = new ArrayList<>();
        // 创建单个客房预订记录
        Reservation reservation = new Reservation();
        reservation.setHotelId(hotelId);
        reservation.setBookingid(colrs.getBookingid());
        reservation.setCrsno(colrs.getCrsno());
        reservation.setOtano(colrs.getOtano());
        reservation.setGuestName(colrs.getBookerName());
        reservation.setTelephone(colrs.getTelephone());
        reservation.setArrivalDate(colrs.getArrivalDate());
        reservation.setDepartureDate(colrs.getDepartureDate());


        reservation.setArrivalDate(req.getStayDateRange().getStartDate());
        reservation.setDepartureDate(req.getStayDateRange().getEndDate());
        reservation.setRoomType(req.getRoomType());
        reservation.setOtano(req.getOtaOrderId());
        reservation.setCrsno(req.getConfirmRoomNumber());
        reservation.setBlock(req.getBlockCode());
        reservation.setRooms(req.getNumberOfRooms());
        reservation.setRateCode(req.getRateCode());
        reservation.setChannel(req.getChannel());

        reservations.add(reservation);
        return reservations;
    }

    @Override
    public Profile getMainProfile() {
        Profile profile = new Profile();
        profile.setGuestName(colrs.getBookerName());
        profile.setTelephone(colrs.getTelephone());
        profile.setHotelId(hotelId);
        return profile;
    }

    @Override
    public String getHotelId() {
        return hotelId;
    }


    public UpdRsParamForm getUpdForm() {
        UpdRsParamForm updRsParamForm = new UpdRsParamForm();  //这里填充 CRS 1.0 可以编辑的请求参数
        updRsParamForm.setArrivalDate(req.getStayDateRange().getStartDate());
        updRsParamForm.setDepartureDate(req.getStayDateRange().getEndDate());
        updRsParamForm.setRateCode(req.getRateCode());
        updRsParamForm.setRoomType(req.getRoomType());
        updRsParamForm.setBlock(req.getBlockCode());
        updRsParamForm.setReservationNumber(req.getPmsRoomOrderId());


        return updRsParamForm;
    }
}

