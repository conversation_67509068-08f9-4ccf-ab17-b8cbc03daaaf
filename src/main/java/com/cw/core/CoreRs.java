package com.cw.core;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.util.DateUtils;
import com.cw.arithmetic.PojoUtils;
import com.cw.arithmetic.SysFunLibTool;
import com.cw.arithmetic.others.GuestCheckinProfileProcessor;
import com.cw.arithmetic.sku.OpskuPickup;
import com.cw.arithmetic.sku.TMultiRsdata;
import com.cw.arithmetic.sku.TSkuUpd;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.OptionSwitchTool;
import com.cw.cache.RedisTool;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.config.exception.BizException;
import com.cw.core.func.order.OrderReqTransFactory;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.model.OtaCheckinResult;
import com.cw.core.model.OtaGuestInfo;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.ReservationMapper;
import com.cw.mapper.RoomMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.core.StdOrderRequest;
import com.cw.pojo.dto.pms.req.option.OptionReq;
import com.cw.pojo.dto.pms.req.profile.ProfileReq;
import com.cw.pojo.dto.pms.req.reservation.BatchAssignRoomReq;
import com.cw.pojo.dto.pms.req.reservation.CheckinVerificationReq;
import com.cw.pojo.dto.pms.req.reservation.ReservationReq;
import com.cw.pojo.dto.pms.req.reservation.UpdRsParamForm;
import com.cw.pojo.dto.pms.req.room.RoomSearchReq;
import com.cw.pojo.dto.pms.res.reservation.*;
import com.cw.pojo.dto.pms.res.room.RoomListRes;
import com.cw.pojo.dto.pms.res.room.RoomRes;
import com.cw.service.config.options.OptionSwitchService;
import com.cw.service.context.GlobalContext;
import com.cw.service.log.UserLogService;
import com.cw.utils.*;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.StatusTypeUtils;
import com.cw.utils.options.Options;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.RedissonMultiLock;
import org.redisson.api.RLock;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 * 订单公共服务方法-入住,退房,换房,取消,延住
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/5/31 16:44
 **/
@Slf4j
@Service
public class CoreRs {

    @Autowired
    CoreAvl coreAvl;

    @Autowired
    CorePrice corePrice;

    @Autowired
    OrderReqTransFactory transFactory;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    ReservationMapper rsMapper;

    @Autowired
    DaoLocal<?> daoLocal;

    @Autowired
    ReservationMapper reservationMapper;
    @Autowired
    private CoreDaily coreDaily;

    @Autowired
    private GuestCheckinProfileProcessor guestCheckinProfileProcessor;

    @Autowired
    public RoomMapper roomMapper;

    @Autowired
    private SeqNoService seqNoService;

    @Resource
    private UserLogService userLogService;

    @Autowired
    OptionSwitchService optionSwitchService;


    public void batchCheckIn() {   //入一笔账.并且发送命令.并刷新订单

    }

    private void reCalcBalance() {


    }

    /**
     * OTA入住方法
     *
     * @param hotelId       酒店ID
     * @param roomOrderIds  预订号列表
     * @param guestInfoList 入住人员信息列表
     * @param contactPhone  联系方式
     * @param userId        操作用户ID
     * @return 入住结果
     * @throws DefinedException 业务异常
     */
    public OtaCheckinResult otaCheckin(String hotelId, List<String> roomOrderIds,
                                       List<OtaGuestInfo> guestInfoList, String contactPhone, String userId) throws DefinedException {

        // 1. 验证参数
        if (CollectionUtil.isEmpty(roomOrderIds)) {
            throw new DefinedException("预订号列表不能为空", ResultCode.FORMERR.code());
        }
        if (CollectionUtil.isEmpty(guestInfoList)) {
            throw new DefinedException("入住人员信息不能为空", ResultCode.FORMERR.code());
        }

        // 2. 查询预订信息
        List<Reservation> reservationList = rsMapper.findByReservationNumbers(roomOrderIds, hotelId);
        if (CollectionUtil.isEmpty(reservationList)) {
            throw new DefinedException("找不到对应的预订信息", ResultCode.FORMERR.code());
        }

        // 3. 验证预订状态和入住条件
        List<Reservation> checkinReservationList = new ArrayList<>();
        Date hotelDate = CalculateDate.getSystemDate();

        for (Reservation reservation : reservationList) {
            // 检查预订状态
            if (reservation.getReservationStatus() != StatusTypeUtils.RsStatus.EXPECTED) {
                throw new DefinedException("预订号 " + reservation.getReservationNumber() + " 状态不是预抵状态，无法入住", ResultCode.FORMERR.code());
            }

            // 检查入住日期（今天或凌晨到店都能入住）
            boolean canCheckin = CalculateDate.isEqual(reservation.getArrivalDate(), hotelDate) ||
                    CalculateDate.isEqual(CalculateDate.reckonDay(reservation.getArrivalDate(), 5, 1), hotelDate);
            if (!canCheckin) {
                throw new DefinedException("预订号 " + reservation.getReservationNumber() + " 不是今日入住，无法办理入住", ResultCode.FORMERR.code());
            }

            // 检查房间号
            if (StrUtil.isBlank(reservation.getRoomNumber())) {
                throw new DefinedException("预订号 " + reservation.getReservationNumber() + " 未分配房间，无法入住", ResultCode.FORMERR.code());
            }

            checkinReservationList.add(reservation);
        }

        // 4. 处理入住人员档案信息
        List<ProfileReq> profileReqList = convertGuestInfoToProfileReq(guestInfoList);
        // 使用Spring管理的Bean，确保事务一致性
        guestCheckinProfileProcessor.processInfo(profileReqList, roomOrderIds, hotelId);

        // 5. 更新预订状态为入住
        for (Reservation reservation : checkinReservationList) {
            Reservation dbReservation = rsMapper.findByHotelIdAndReservationNumber(hotelId, reservation.getReservationNumber());
            dbReservation.setReservationStatus(StatusTypeUtils.RsStatus.CHECKIN);
            if (StrUtil.isNotBlank(contactPhone)) {
                dbReservation.setTelephone(contactPhone);
            }
            rsMapper.save(dbReservation);

            log.info("OTA 办理入住成功 {} ", dbReservation.getRoomNumber());

            // 记录日志
            String content = StrUtil.format("自助办理入住成功{}", dbReservation.getRoomNumber());
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, userId,
                    reservation.getReservationNumber(), content, hotelId);
        }

        // 6. 更新房间状态（如果开启了入住自动脏房选项）
        for (Reservation reservation : checkinReservationList) {
            boolean inRange = CalculateDate.isInRange(CalculateDate.getSystemDate(),
                    reservation.getArrivalDate(), reservation.getDepartureDate());
            //if (inRange && OptionSwitchTool.getOptionSatus(Options.CI_AUTODI, hotelId)) {
            //    roomMapper.updateRoomLocc(reservation.getRoomNumber(), reservation.getHotelId());
            //}
            roomMapper.updateRoomLocc(reservation.getRoomNumber(), reservation.getHotelId());
        }

        //TODO




        // 7. 构建返回结果
        return buildOtaCheckinResult(checkinReservationList, guestInfoList, contactPhone);
    }

    public RsOrderResult createBatchOrder(StdOrderRequest stdOrderRequest) throws DefinedException {
        String hotelId = stdOrderRequest.getHotelId();

        //第一步 解析订单请求.并写入订单号.付款单号
        StdOrderData orderData = transFactory.parseOrderForm(stdOrderRequest);
        orderData.setHotelId(hotelId);
        transFactory.writeMakerAndRegno(orderData);
        orderData.setHotelId(hotelId);


        //第二步 等待库存联锁加锁成功 .先在 redis 预扣库存
        //checkCache(hotelId, orderData);

        //第三步 在锁定资源的条件下.将订单写入数据库.并发送命令.并刷新订单
        RsOrderResult rsOrderResult = diapatchCreateOrderAction(orderData);

        return rsOrderResult;
    }


    public void cancelOrder(String hotelId, String orderNo) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, orderNo);
        if (rs == null) {
            throw new DefinedException("订单不存在", ResultCode.FORMERR.code());
        }
        if (!CalculateNumber.isZero(rs.getBalance())) {
            throw new DefinedException("订单余额不为0.请先调整账务.退房失败", ResultCode.FORMERR.code());
        }

        if (StrUtil.isNotBlank(rs.getSuino()) && StrUtil.startWith(rs.getSuino(), "M")) {
            String number = rs.getSuino().replaceAll("M","");
            // 套房关联的子预订
            List<Reservation> reservationList = rsMapper.findByHotelIdAndSuino(hotelId, number);
            for (Reservation reservation : reservationList) {
                cancelOrder(hotelId,reservation.getReservationNumber());
            }
        }
        rs.setReservationStatus(StatusTypeUtils.RsStatus.CANCEL);
        daoLocal.merge(rs);

        TMultiRsdata olddata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(rs.getBlock()));
        olddata.fillArray(ProdType.ROOM.val(), Reservation.class, Arrays.asList(rs));

        TMultiRsdata newdata = new TMultiRsdata(true, StrUtil.EMPTY);
        List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);
        coreAvl.updateResourceAvailability(updList, hotelId, ProdType.ROOM.val());
        //取消订单需要更新房间状态，修复缺陷对应的问题：IDTQDB-135
        coreAvl.updRoomOccStatus(rs.getRoomNumber(), StrUtil.EMPTY, hotelId, true);
    }

    public void cancelCheckIn(String hotelId, String reservationNumber, String userId) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, reservationNumber);
        if (rs == null) {
            throw new DefinedException("订单不存在", ResultCode.FORMERR.code());
        }
        if (!CalculateDate.isEqual(rs.getArrivalDate(), new Date()) || rs.getReservationStatus() != StatusTypeUtils.RsStatus.CHECKIN) {
            throw new DefinedException("只能取消当日入住订单", ResultCode.FORMERR.code());
        }
        rs.setReservationStatus(StatusTypeUtils.RsStatus.EXPECTED);
        daoLocal.merge(rs);
        /*//TODO 取消订单是否需要将对应的房间状态恢复为占用的干净房状态,暂且处理，后续觉得不需要，移除即可？
        //此处只处理当前的房间状态，未来的房间预定不影响当前的房间状态
        boolean inRange = CalculateDate.isInRange(CalculateDate.getSystemDate(), rs.getArrivalDate(), rs.getDepartureDate());
        //if (inRange) {
            roomMapper.updateRoomLoccAndDirty(StatusTypeUtils.RoomLocc.OCCUPY,StatusTypeUtils.RoomStatus.CLEAN,new Date(),
                    GlobalContext.getCurrentUserId(),rs.getRoomNumber(),rs.getHotelId());
        }*/
    }

    public void checkIn(String hotelId, String orderNo, String userId) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, orderNo);
        if (rs == null) {
            throw new DefinedException("订单不存在", ResultCode.FORMERR.code());
        }
        rs.setReservationStatus(StatusTypeUtils.RsStatus.CHECKIN);
        daoLocal.merge(rs);
        //TODO 入住自动变脏房
    }


    /**
     * 恢复取消.NOSHOW 订单
     *
     * @param hotelId
     * @param orderNo
     * @param userId
     * @return
     * @throws DefinedException
     */
    public Reservation internalReactiveReservation(String hotelId, String orderNo, String userId) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, orderNo);
        Reservation orgRs = PojoUtils.cloneEntity(rs);  //应用更改之前先做
        if (rs == null) {
            throw new DefinedException("订单不存在", ResultCode.FORMERR.code());
        }

        if (rs.getReservationStatus() == StatusTypeUtils.RsStatus.CANCEL ||
                CalculateDate.isBefore(rs.getArrivalDate(), CalculateDate.getSystemDate())) {
            if (CalculateDate.isBefore(rs.getArrivalDate(), CalculateDate.getSystemDate())) {
                rs.setArrivalDate(CalculateDate.getSystemDate());
            }
            if (CalculateDate.isBefore(rs.getDepartureDate(), CalculateDate.getSystemDate())) {
                rs.setDepartureDate(CalculateDate.getSystemDate());
            }
        }

        rs.setReservationStatus(StatusTypeUtils.RsStatus.EXPECTED);

        List<TSkuUpd> updList = checkUpdForm(rs, orgRs);  //恢复时检查房量
        if (CollectionUtil.isNotEmpty(updList)) {
            coreAvl.updateResourceAvailability(updList, hotelId, ProdType.ROOM.val());
        }

        List<RoomsDaily> dailyList = corePrice.writeRoomPrice(rs, false);
        coreDaily.updateRsDailys(rs, dailyList);

        String content = StrUtil.format("恢复订单");
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                orgRs.getReservationNumber(), content, GlobalContext.getCurrentUserId());

        return rs;
    }





    /**
     * 将客户端提交的订单数据更新到数据库
     *换房.延住.保存预订信息可以用这个方法.
     * @param paramForm  要修改的预订信息.将修改的字段填充到这个对象中.
     * @param hotelId
     * @param userId
     * @throws DefinedException
     */
    public Reservation changeRsParaSave(UpdRsParamForm paramForm, String hotelId, String userId) throws DefinedException {//修改单个预订
        //boolean dontcalcPrice=CalculateDate.isEqual(rs.getAnreise_p(), arrival)
        //        &&CalculateDate.isEqual(rs.getAbreise_p(), departure)&&rs.getKat_p().equals(roomType)
        //        &&!rs.getZimmer_p().equals(newRoomNo);//如果只是换房。不重算房价

        Reservation dbRs = getCheckedReservation(paramForm.getReservationNumber(), hotelId);
        Reservation orgRs = PojoUtils.cloneEntity(dbRs);  //应用更改之前先做

        BeanUtil.copyProperties(paramForm, dbRs, CopyOptions.create().setIgnoreNullValue(true));


        List<TSkuUpd> updList = checkUpdForm(dbRs, orgRs);  //检查提交过来的表单是否满足要求

        checkUpdReservation(orgRs, dbRs, updList);

        if (CollectionUtil.isNotEmpty(updList)) {
            coreAvl.updateResourceAvailability(updList, hotelId, ProdType.ROOM.val());
        }

        List<RoomsDaily> dailyList = corePrice.writeRoomPrice(dbRs, false);

        updBuildingNo(dbRs);

        rsMapper.save(dbRs);

        coreDaily.updateRsDailys(dbRs, dailyList);

        coreAvl.updRoomOccStatus(orgRs.getRoomNumber(), dbRs.getRoomNumber(), dbRs.getHotelId(), false);


        ////判断价格.入住日期.房间号变化.写LOG
        ////修改变更记录信息汇总 存入
        BeanChangeUtil<Reservation> t = new BeanChangeUtil<>();
        String changeInfo = t.contrastObj(orgRs, dbRs, dbRs.getHotelId());
        if (StringUtils.isNotBlank(changeInfo)) {
            String content = "[修改预定:" + dbRs.getReservationNumber() + "]\n" + changeInfo;
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                    dbRs.getReservationNumber(), content, GlobalContext.getCurrentHotelId());
        }

        return dbRs;
        //更新预订信息
    }

    private List<TSkuUpd> checkUpdForm(Reservation updReservation, Reservation orgReservation) throws DefinedException {
        boolean lcheckRoomOcc = StrUtil.isNotBlank(updReservation.getRoomNumber()) && !updReservation.getRoomNumber().equals(orgReservation.getRoomNumber());

        boolean lreactive = orgReservation.getReservationStatus().equals(StatusTypeUtils.RsStatus.CANCEL) && !orgReservation.getReservationStatus().equals(updReservation.getReservationStatus());

        List<TSkuUpd> updList = null;
        //修改BLOCK.入住日期.离店日期.房间数.房型 要做可卖校验
        boolean lcheckAvl = !updReservation.getBlock().equals(orgReservation.getBlock()) ||
                !CalculateDate.isEqual(updReservation.getArrivalDate(), orgReservation.getArrivalDate()) ||
                !CalculateDate.isEqual(updReservation.getDepartureDate(), orgReservation.getDepartureDate()) ||
                !CalculateNumber.isequals(updReservation.getRooms(), orgReservation.getRooms()) ||
                !updReservation.getRoomType().equals(orgReservation.getRoomType()) ||
                !updReservation.getRooms().equals(orgReservation.getRooms()) ||
                lreactive;//订单恢复

        if (lcheckRoomOcc && !CalculateDate.isEqual(updReservation.getArrivalDate(),
                updReservation.getArrivalDate())) {//检查目标房间当前是否分配有预订

            checkRoomOccupied(updReservation.getRoomNumber(), updReservation.getReservationNumber(), updReservation.getArrivalDate(), updReservation.getDepartureDate(), updReservation.getHotelId());
            log.info("房间占用检查通过.预订号{}房型  {}", updReservation.getReservationNumber(), updReservation.getRoomType());
        }

        if (lcheckAvl) {
            TMultiRsdata olddata = new TMultiRsdata(lreactive ? true : false, SysFunLibTool.getAvlSource(orgReservation.getBlock()));
            olddata.fillArray(ProdType.ROOM.val(), Reservation.class, Arrays.asList(orgReservation));
            TMultiRsdata newdata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(updReservation.getBlock()));
            newdata.fillArray(ProdType.ROOM.val(), Reservation.class, Arrays.asList(updReservation));
            updList = OpskuPickup.calcSkuPickup(olddata, newdata);


            log.info("房间可卖检查:. 当前预订要更改的日期{}.房型{}.房间数{}", CalculateDate.dateToString(updReservation.getArrivalDate()) + "-" + CalculateDate.dateToString(updReservation.getDepartureDate()),
                    updReservation.getRoomType(), updReservation.getRooms());

            coreAvl.checkDBSkuLeft(updReservation.getHotelId(), updList); //该

            //coreAvl.checkCacheSku(updReservation.getHotelId(),updList);//TODO
        }

        return updList;
    }

    public String checkRoomOccupied(String roomNumber, String reservationNumber, Date arrivalDate, Date departureDate, String hotelId) throws DefinedException {
        List<String> occRss = rsMapper.fetchRoomOccupiedRs(roomNumber,
                reservationNumber,
                arrivalDate, departureDate, hotelId);

        if (occRss.size() > 0) {
            log.error("目标房间{}当前已有预订{},请选择其他房间", roomNumber, occRss);
            throw new DefinedException("目标房间当前已有预订,请选择其他房间", ResultCode.FORMERR.code());
        }
        return StrUtil.EMPTY;
    }

    /**
     * 预订窗口检查校验
     *
     * @param orgReservation
     * @param updReservation
     * @param updList
     * @throws DefinedException
     */
    public void checkUpdReservation(Reservation orgReservation, Reservation updReservation, List<TSkuUpd> updList) throws DefinedException {
        boolean lcheckRoomOcc = StrUtil.isNotBlank(updReservation.getRoomNumber())  //当前订单还有房间号
                && (updReservation.getRoomNumber().equals(orgReservation.getRoomNumber())
                || !CalculateDate.isEqual(updReservation.getArrivalDate(), orgReservation.getArrivalDate()) ||
                !CalculateDate.isEqual(updReservation.getDepartureDate(), orgReservation.getDepartureDate()));


        boolean lcheckAvl = !updReservation.getBlock().equals(orgReservation.getBlock()) ||
                !CalculateDate.isEqual(updReservation.getArrivalDate(), orgReservation.getArrivalDate()) ||
                !CalculateDate.isEqual(updReservation.getDepartureDate(), orgReservation.getDepartureDate()) ||
                !CalculateNumber.isequals(updReservation.getRooms(), orgReservation.getRooms()) ||
                !updReservation.getRoomType().equals(orgReservation.getRoomType()) ||
                !updReservation.getRooms().equals(orgReservation.getRooms());

        if (lcheckRoomOcc) {//检查目标房间当前是否分配有预订
            checkRoomOccupied(updReservation.getRoomNumber(), updReservation.getReservationNumber(), updReservation.getArrivalDate(), updReservation.getDepartureDate(), updReservation.getHotelId());
        }

        if (lcheckAvl) {//检查房型可卖数
            coreAvl.checkDBSkuLeft(updReservation.getHotelId(), updList);
        }

        //TODO 判断下.如果是联通房的.暂时不允许延住.换房

    }


    public void checkOut(String hotelId, String orderNo, String userId) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, orderNo);
        boolean lstatuok = rs.getReservationStatus() == StatusTypeUtils.RsStatus.CHECKIN || rs.getReservationStatus() == StatusTypeUtils.RsStatus.PENDING_CHECKOUT;
        if (!lstatuok) {
            if (rs.getReservationStatus() == StatusTypeUtils.RsStatus.CHECKOUT) {
                return; //已经退房的直接返回
            } else {
                throw new DefinedException("订单状态不是入住或者待结账状态.不允许退房", ResultCode.FORMERR.code());
            }
        }
        if (!CalculateNumber.isZero(rs.getBalance())) {
            throw new DefinedException("订单余额不为0.请先调整账务.退房失败", ResultCode.FORMERR.code());
        }

        if (CalculateDate.isAfter(rs.getDepartureDate(), CalculateDate.getSystemDate())) {
            rs.setDepartureDate(CalculateDate.getSystemDate());//如果是提前离店.账务调平.可以将离店日期设置为当前日期
        }

        rs.setReservationStatus(StatusTypeUtils.RsStatus.CHECKOUT);
        daoLocal.merge(rs);

        coreAvl.updRoomOccStatus(rs.getRoomNumber(), StrUtil.EMPTY, hotelId, true);

        String content = StrUtil.format("退房：房间号:{}", rs.getRoomNumber());
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, userId,
                rs.getReservationNumber(), content, hotelId);


    }

    private void checkCache(String projectId, StdOrderData data) throws DefinedException {
        // 第二步 等待库存联锁加锁成功 .先在 redis 预扣库存
        List<TSkuUpd> updList = coreAvl.getOrderSkuUpd(null, data);//解析占用数
        List<String> lockKeys = OpskuPickup.getLockKeys(projectId, updList);

        boolean llock = false;
        RedissonMultiLock multiLock = null;
        RLock[] rLocks = getRLocks(lockKeys.toArray(new String[]{}));
        if (rLocks.length == 0) {
            return;
        }
        multiLock = new RedissonMultiLock(rLocks);
        try {
            llock = multiLock.tryLock(3, 2, TimeUnit.SECONDS); //等待5秒.锁有效期3秒.5秒之内足够扣减数据库库存.与redis库存
        } catch (InterruptedException e) {
            log.error(e.getMessage());
        }
        if (!llock) {
            throw new DefinedException("订单请求处理正忙,请重试", ResultCode.FORMERR.code());
        }
        //资源锁成功后.做资源库存扣减
        if (data.getRooms().size() > 0) {
            try {
                coreAvl.checkCacheSku(projectId, updList);//再判断一次redis库存是否足够.因为有可能等待过程中.上一个锁用户已经消费了库存
            } catch (DefinedException e) {
                multiUnlock(multiLock);
                throw e;
            }
            coreAvl.updateSkuCache(projectId, updList); //马上扣减相关的缓存库存
        }
        if (llock) {
            multiUnlock(multiLock);
        }
    }

    private RLock[] getRLocks(String[] skuid) {
        RLock[] lockarray = new RLock[skuid.length];
        for (int i = 0; i < skuid.length; i++) {
            lockarray[i] = RedisTool.getRedissonClient().getLock(skuid[i]);
        }
        return lockarray;
    }

    private void multiUnlock(RedissonMultiLock multiLock) {
        if (multiLock != null) {
            multiLock.unlock();
        }
    }


    private RsOrderResult diapatchCreateOrderAction(StdOrderData orderData) throws DefinedException {
        //Booking_rs bookingRs = orderData.getBookingRs();

        checkOrderRequest(orderData);//检查所有表单的日期.不能小于当前日期

        checkAvl(orderData); //检查数据库库存

        updPriceAndFill(orderData);//计算价格.并填充

        //orderHandler.createInitialOrder(orderData);//提交订单到 接口

        writeCreateOrder2DataDb(orderData);//更新写入数据库

        RsOrderResult orderResult = new RsOrderResult(); // getCreateNewOrderResult(col_rs, orderData.getPricetype());

        orderResult.setRelationNumber(orderData.getRooms().get(0).getRelationNumber());
        orderResult.setReservaitonNumber(orderData.getRooms().get(0).getReservationNumber());
        orderResult.setTotalAmount(orderData.getTotalAmount());

        for (Reservation room : orderData.getRooms()) {
            orderResult.getRoomIds().add(new RsOrderResult.IdResult(room.getReservationNumber(), room.getCrsno()));
        }
        if (orderData.getColrs() != null) {
            orderResult.setColIds(new RsOrderResult.IdResult(orderData.getColrs().getBookingid(), orderData.getColrs().getCrsno()));
        }


    /*    if (CalculateNumber.isGreaterThanZero(orderData.getActualAmount())) { //如果订单不需要产生支付.就不需要放到自动取消队列里  判断一下如果是自助入住机创建的订单.
            int exprieMin = 10; //预订10分钟后自动取消订单  暂定值
            long countDown = DateUtil.offset(new Date(), DateField.MINUTE, exprieMin).getTime();
            log.info("订单创建成功 将在{} 取消订单", DateUtil.format(DateUtil.date(countDown), "yyyy-MM-dd HH:mm:ss"));

            Bussness_CancelNoPayMsg msg = new Bussness_CancelNoPayMsg();
            msg.setReservationNo(orderData.getRooms().get(0).getReservationNumber());
            msg.setHotelId(orderData.getRooms().get(0).getHotelId());
            msg.setAddTime(DateUtil.format(DateUtil.date(countDown), "yyyy-MM-dd HH:mm:ss"));

            int extraTime = 20;//实际额外的自动取消时间.暂时先加个20秒 .微信支付用户输密码是不算超时的

            String delaySignal = MqNameUtils.getDelaySignal(MqNameUtils.DirectExchange.PMS, MqNameUtils.BussinessTask.CANCELNOPAY.name());
            ExpireMessagePostProcessor expire = new ExpireMessagePostProcessor(TimeUnit.MINUTES.toSeconds(exprieMin) + extraTime, TimeUnit.SECONDS);//延迟个5秒.防止跟关闭交易时间有冲突
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.DELAY.name(), delaySignal,
                    JSON.toJSONString(msg), expire);
        }*/
        return orderResult;
    }

    private void checkOrderRequest(StdOrderData orderData) throws DefinedException {
        Date systemDate = CalculateDate.getSystemDate();
        if (!orderData.getRooms().isEmpty()) {
            for (Reservation room : orderData.getRooms()) {
                if (CalculateDate.isBefore(room.getArrivalDate(), systemDate)) {
                    throw new DefinedException("客房预订日期不能早于当前日期", ResultCode.FORMERR.code());
                }

                if (StrUtil.isNotBlank(room.getRoomNumber())) {
                    //检查该房间号 在入住期间是否被占用
                    checkRoomOccupied(room.getRoomNumber(), room.getReservationNumber(), room.getArrivalDate(), room.getDepartureDate(), room.getHotelId());
                }
            }
        }
    }


    private void checkAvl(StdOrderData orderData) throws DefinedException {
        //检查数据库库存
        if (!orderData.getRooms().isEmpty()) {//现在没有 block .但是是给后面的促销活动做准备
            Map<String, List<Reservation>> blockRoomsMap = orderData.getRooms().stream().collect(Collectors.groupingBy(Reservation::getBlock));
            for (Map.Entry<String, List<Reservation>> blockEntry : blockRoomsMap.entrySet()) {
                HashMap<String, Object> checkResult = coreAvl.checkBatchCreateAvailability(blockEntry.getValue(), blockEntry.getKey(), blockEntry.getValue().get(0).getHotelId());
                if (checkResult.size() > 0) {  //如果这些客房预订都来自不同的 BLOCK. 就批量循环检查
                    String errmsg = "";
                    for (Object o : checkResult.values()) {
                        errmsg += errmsg.isEmpty() ? o.toString() : "," + o.toString();
                    }
                    throw new DefinedException(errmsg, ResultCode.OVERBOOK_ERR.code());
                }
            }

            for (Reservation rs : orderData.getRooms()) {
                if (StrUtil.isNotBlank(rs.getRoomNumber())) {
                    checkRoomOccupied(rs.getRoomNumber(), rs.getReservationNumber(), rs.getArrivalDate(), rs.getDepartureDate(), rs.getHotelId());
                }
            }
        }
    }


    private void updPriceAndFill(StdOrderData orderData) throws DefinedException {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Reservation room : orderData.getRooms()) {
            List<RoomsDaily> dailyList = corePrice.writeRoomPrice(room, false);  //客房订单每日房价详情
            totalAmount = totalAmount.add(room.getTotalPrice());
            orderData.getRoomsDailyMap().putAll(room.getReservationNumber(), dailyList);

            updBuildingNo(room);
        }

        orderData.setTotalAmount(totalAmount);
        orderData.setActualAmount(totalAmount);
    }


    public void updBuildingNo(Reservation room) {
        RoomTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
        RoomType roomType = cache.getRecord(room.getHotelId(), room.getRoomType());
        if (roomType != null) {
            room.setBuildingNo(roomType.getBuildingNo());
        }
    }


    public void writeCreateOrder2DataDb(StdOrderData orderData) throws DefinedException {
        CoreDaily coreDaily = SpringUtil.getBean(CoreDaily.class);
        for (Reservation room : orderData.getRooms()) {
            List<TSkuUpd> updList = coreAvl.getOrderSkuUpd(null, orderData);
            List<TSkuUpd> roomUpdList = OpskuPickup.splitSkus(updList, ProdType.ROOM.val());
            coreAvl.updateResourceAvailability(roomUpdList, orderData.getHotelId(), ProdType.ROOM.val());//扣减数据库库存
            daoLocal.merge(room); //写入数据库

            List<RoomsDaily> dailyList = Lists.newArrayList(orderData.getRoomsDailyMap().get(room.getReservationNumber()));
            coreDaily.updateRsDailys(room, dailyList);
            coreDaily.writePkgDaily(room);

            updBuildingNo(room);

            String content = StrUtil.format("创建预定，预定号：{}", room.getReservationNumber());
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, GlobalContext.getCurrentUserId(),
                    room.getReservationNumber(), content, orderData.getHotelId());

        }

        if (orderData.getColrs() != null) {
            Colrs colrs = daoLocal.merge(orderData.getColrs());
        }
    }


    private Reservation getCheckedReservation(String reservationNumber, String hotelId) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, reservationNumber);
        if (rs == null) {
            throw new DefinedException("订单记录没有找到");
        }

        if (!(rs.getReservationStatus() == StatusTypeUtils.RsStatus.EXPECTED ||
                rs.getReservationStatus() == StatusTypeUtils.RsStatus.CHECKIN)) {
            throw new DefinedException("订单已经取消或者离店.不允许修改");
        }

        //CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
        //BeanUtil.copyProperties(newRs,rs, copyOptions);
        rs.setModifiedTime(new Date());

        return rs;
    }


    //private void updHotelCode(Reservation rs){
    //    RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
    //    RoomType roomType = roomTypeCache.getRecord(rs.getHotelId(), rs.getRoomType());
    //    if(roomType!= null){
    //        BuildingCache buildingCache = GlobalCache.getDataStructure().getCache(GlobalDataType.BUILDING);
    //        Building building = buildingCache.getRecord(rs.getHotelId(), roomType.getBuildingNo());
    //        if(building!= null){
    //            rs.setBuildingNo(building.getCode());
    //        }
    //    }
    //}


    public BatchAssginRoomResult batchAssignRoomReq(BatchAssignRoomReq req, String hotelId) {
        BatchAssginRoomResult results = new BatchAssginRoomResult();
        results.getRegNos().addAll(req.getRegNos());

        if (req.isLclear()) {
            // 需要取消分房的逻辑保持不变
            for (String regNo : req.getRegNos()) {
                AssResult assResult = clearRoomNo(regNo, hotelId);
                results.getAssResults().add(assResult);
            }
            return results;
        }

        boolean lmanual = !req.isLclear() && CollectionUtil.isNotEmpty(req.getAssignRoomNos());

        for (String regNo : req.getRegNos()) {
            Reservation reservation = rsMapper.findByHotelIdAndReservationNumber(hotelId, regNo);
            if (reservation == null || !reservation.getReservationStatus().equals(StatusTypeUtils.RsStatus.EXPECTED)) {
                AssResult assResult = new AssResult();
                assResult.setName(reservation == null ? regNo : reservation.getGuestName());
                assResult.setResult(false);
                results.getAssResults().add(assResult);
                continue;
            }

            // 如果预订房间数大于1且没有分配房间号，需要拆分
            if (reservation.getRooms() > 1 && StrUtil.isBlank(reservation.getRoomNumber())) {//如果当前在给一个多间房的预订分房.
                // 拆分预订
                Integer roomCount = reservation.getRooms();
                for (int i = 0; i < roomCount; i++) {
                    if (reservation.getRooms() > 1) {
                        Reservation splitRs = splitReservation(reservation);//TODO  reservation 对象需要每次重新加载一次
                        results.getRegNos().add(splitRs.getReservationNumber());
                        // 为拆分出的预订分配房间
                        boolean lassOk = assignRoomToReservation(splitRs, req, hotelId, results, lmanual);
//                        if (!lassOk) {
//                            break;
//                        } else {
//                            reservation = rsMapper.findByHotelIdAndReservationNumber(hotelId, regNo);// 每次分房之后都需要重新加载一次预订
//                        }
                    } else {
                        //拆分之后还剩下自己的话. 再把自己分出去
                        boolean lassOk = assignRoomToReservation(reservation, req, hotelId, results, lmanual);
                    }

                }


            } else if (StrUtil.isBlank(reservation.getRoomNumber())) {
                // 单房间预订直接分配房间
                assignRoomToReservation(reservation, req, hotelId, results, lmanual);
            }
        }
        return results;
    }

    private AssResult clearRoomNo(String rsNo, String hotelId) {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, rsNo);
        AssResult result = new AssResult();
        if (rs != null) {
            if (rs.getReservationStatus() == StatusTypeUtils.RsStatus.EXPECTED) {
                rs.setRoomNumber(StrUtil.EMPTY);
                rsMapper.save(rs);
                userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(), rs.getReservationNumber(), "取消分房", hotelId);
                result.setName(rs.getGuestName());
                result.setResult(true);
                return result;
            }
        }
        return result;
    }


    private String seekFreeRoom(RoomSearchReq req,
                                String hotelId) {
        RoomListRes res = getAvlRoomPage(req, hotelId);
        if (res.getRecords().size() > 0) {
            return res.getRecords().get(0).getRoomNo();
        }
        return StrUtil.EMPTY;
    }

/*    public List<Room> getAvlRoomTable(RoomSearchReq req, String hotelId) {
        List<Room> rooms = Lists.newArrayList();
        boolean lneedRmtFilter = CollectionUtil.isNotEmpty(req.getRoomTypes());
        String rsfilter = "SELECT res.roomNumber " +
                "            FROM Reservation res " +
                "            WHERE res.hotelId = :hotelId" +
                (lneedRmtFilter ? "            AND res.roomType in :roomType" : "") +
                "            AND res.reservationStatus IN (0, 1) " +
                "          AND res.roomNumber IS NOT NULL " +
                "         AND ((  res.arrivalDate <= :arrdate  AND res.departureDate " + (req.isIncDueout() ? ">=" : ">") + " :arrdate) or (res.arrivalDate >=:arrdate and res.departureDate<=:depdate))";
        String jpql = "  FROM Room r " +
                "        WHERE r.hotelId = :hotelId" +
                (lneedRmtFilter ? "        AND r.roomType in  :roomType " : "") +
                "        AND r.roomNo NOT IN (" + rsfilter + " )";

        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("hotelId", hotelId);
        queryMap.put("arrdate", req.getArrDate());
        queryMap.put("depdate", req.getDeptDate());
        if (lneedRmtFilter) {
            queryMap.put("roomType", req.getRoomTypes());
        }
        rooms = daoLocal.getList(jpql, queryMap);
        return rooms;
    }*/

    public RoomListRes getAvlRoomPage(RoomSearchReq req, String hotelId) {
        boolean lneedRmtFilter = CollectionUtil.isNotEmpty(req.getRoomTypes());
        String rsfilter = "SELECT res.roomNumber " +
                "            FROM Reservation res " +
                "            WHERE res.hotelId = :hotelId" +
                (lneedRmtFilter ? "            AND res.roomType in :roomType" : "") +
                "            AND res.reservationStatus IN (0, 1) " +
                "          AND res.roomNumber IS NOT NULL " +
                "         AND ((  res.arrivalDate <= :arrdate  AND res.departureDate " + (req.isIncDueout() ? ">=" : ">") + " :arrdate) or (res.arrivalDate >=:arrdate and res.departureDate<=:depdate))";
        String jpql = "  FROM Room r " +
                "        WHERE r.hotelId = :hotelId" +
                (lneedRmtFilter ? "        AND r.roomType in  :roomType " : "") +
                "        AND r.roomNo NOT IN (" + rsfilter + " )";

        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("hotelId", hotelId);
        queryMap.put("arrdate", req.getArrDate());
        queryMap.put("depdate", req.getDeptDate());
        if (lneedRmtFilter) {
            queryMap.put("roomType", req.getRoomTypes());
        }
        Page<Room> roomPage = daoLocal.getListPage(jpql, queryMap, req.getPages().getPageable());
        RoomListRes roomListRes = new RoomListRes();
        roomListRes.fillPageData(roomPage);

        if (req.isNeedRmtGroup()) {  //批量分房时.需要进行分组
            Map<String, List<RoomRes>> rmtMaps = Maps.newHashMap();
            List<RoomRes> ls = roomListRes.getRecords();
            for (RoomRes room : ls) {
                rmtMaps.computeIfAbsent(room.getRoomType(), k -> new ArrayList<>()).add(room);
            }
            roomListRes.setRmtGroupInfos(new ArrayList<>());
            for (Map.Entry<String, List<RoomRes>> stringListEntry : rmtMaps.entrySet()) {
                RoomListRes.RmtGroupInfo rmtGroupInfo = new RoomListRes.RmtGroupInfo();
                rmtGroupInfo.setRoomType(stringListEntry.getKey());
                rmtGroupInfo.setDescription(CustomData.getDesc(hotelId, rmtGroupInfo.getRoomType(), SystemUtil.CustomDataKey.roomtype));
                List<RoomRes> groupnodes = Lists.newArrayList();
                for (RoomRes value : stringListEntry.getValue()) {
                    groupnodes.add(PojoUtils.cloneEntity(value));
                }
                rmtGroupInfo.setRooms(groupnodes);
                roomListRes.getRmtGroupInfos().add(rmtGroupInfo);
            }
        }
        return roomListRes;
    }

    private Reservation splitReservation(Reservation originalRs) {
        Reservation splitRs = PojoUtils.cloneEntity(originalRs);

        splitRs.setReservationStatus(StatusTypeUtils.RsStatus.EXPECTED);
        splitRs.setRooms(1);
        splitRs.setReservationNumber(seqNoService.getSequenceID(SystemUtil.SequenceKey.ORDERID));
        splitRs.setBalance(BigDecimal.ZERO);
        splitRs.setConsume(BigDecimal.ZERO);
        splitRs.setDeposit(BigDecimal.ZERO);
        splitRs.setHotelId(originalRs.getHotelId());
        // 算总价前先清零当前预订的totalPrice
        splitRs.setTotalPrice(BigDecimal.ZERO);
        splitRs = JpaUtil.appendEntity(splitRs);
        coreDaily.copyRsDailys(splitRs.getArrivalDate(), originalRs.getReservationNumber(),
                splitRs.getReservationNumber(), originalRs.getHotelId(), splitRs);
        // 保存拆分的预订
        rsMapper.save(splitRs);
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, GlobalContext.getCurrentUserId(),
                splitRs.getReservationNumber(), "拆分预订", splitRs.getHotelId());

        // 更新原预订的房间数
        originalRs.setRooms(originalRs.getRooms() - 1);
        try {
            corePrice.writeRoomPrice(originalRs, false); //修改房间数后.刷新一下总价
        } catch (DefinedException e) {
            e.printStackTrace();
        }
        rsMapper.save(originalRs);

        return splitRs;
    }

    private boolean assignRoomToReservation(Reservation reservation, BatchAssignRoomReq req,
                                            String hotelId, BatchAssginRoomResult results, boolean lmanual) {
        String newRoomno = null;
        if (lmanual) {
            newRoomno = req.pushARoomNo(reservation.getRoomType());
        } else {
            RoomSearchReq searchReq = new RoomSearchReq();
            searchReq.setArrDate(reservation.getArrivalDate());
            searchReq.setDeptDate(reservation.getDepartureDate());
            searchReq.setRoomTypes(Arrays.asList(reservation.getRoomType()));
            searchReq.setFloor(req.getFloor());
            newRoomno = seekFreeRoom(searchReq, hotelId);
        }

        if (StrUtil.isNotBlank(newRoomno)) {

            List<String> occRss = rsMapper.fetchRoomOccupiedRs(newRoomno,
                    reservation.getReservationNumber(),
                    reservation.getArrivalDate(), reservation.getDepartureDate(), hotelId);

            if (CollectionUtil.isNotEmpty(occRss)) {
                AssResult assResult = new AssResult();
                assResult.setName(reservation.getGuestName());
                assResult.setResult(false);
                assResult.setRoomNumber(newRoomno);
                results.getAssResults().add(assResult);
                return false;
            }

            reservation.setRoomNumber(newRoomno);
            rsMapper.save(reservation);

            boolean inRange = CalculateDate.isInRange(CalculateDate.getSystemDate(),
                    reservation.getArrivalDate(), reservation.getDepartureDate());
            if (inRange) {
                roomMapper.updateRoomLocc(reservation.getRoomNumber(), reservation.getHotelId());
            }

            userLogService.writeLog(SystemUtil.UserLogType.COL,
                    SystemUtil.UserLogOpType.MODIFY,
                    GlobalContext.getCurrentUserId(),
                    reservation.getReservationNumber(),
                    "分房->" + newRoomno,
                    hotelId);

            AssResult assResult = new AssResult();
            assResult.setName(reservation.getGuestName());
            assResult.setResult(true);
            assResult.setRoomNumber(newRoomno);
            results.getAssResults().add(assResult);
            return true;
        }

        return false;
    }


    public Reservation seekColPostRs(String netWorkId, String crsNo, String hotelId) {
        Colrs colrs = daoLocal.getObject("from Colrs c where c.crsno = ?1 and c.hotelId = ?2", netWorkId, hotelId);
        if (colrs == null) {
            throw new BizException("订单记录没有找到");
        }
        Reservation rs = daoLocal.getObject("from Reservation r where r.relationNumber = ?1 and r.hotelId = ?2  " +
                "  order by reservationNumber asc ", colrs.getBookingid(), hotelId);
        if (rs == null) {
            throw new BizException("订单记录没有找到");
        }
        return rs;

    }

    public RsOrderResult copyReservation(String hotelId, String orderNo) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, orderNo);
        if (rs == null) {
            throw new DefinedException("订单不存在", ResultCode.FORMERR.code());
        }
        ReservationReq reservationReq = new ReservationReq();
        BeanUtil.copyProperties(
                rs
                , reservationReq
                , CopyOptions.create().ignoreNullValue().setIgnoreProperties(
                        "id"
                        ,"reservationNumber"
                        ,"roomNumber"
                        ,"guestName"
                        ,"reservationStatus"
                        ,"rooms"));

        Date hotelDate = CalculateDate.getSystemDate();
        // 到店日期早于酒店日期，则到店日期为当前酒店日期
        if (CalculateDate.isBefore(rs.getArrivalDate(), hotelDate)) {
            reservationReq.setArrivalDate(DateUtils.format(hotelDate));
        }
        // 复制的预订离店日期等于今天的话，默认日期+1
        if (CalculateDate.isEqual(rs.getDepartureDate(), hotelDate) || CalculateDate.isAfter(hotelDate, rs.getDepartureDate())) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(hotelDate);
            calendar.add(Calendar.DAY_OF_YEAR, 1);
            Date nextDay = calendar.getTime();
            reservationReq.setDepartureDate(DateUtils.format(nextDay));
        }
        // 默认为1间房
        reservationReq.setRooms(1);
        reservationReq.setGuestName(String.format("%s_copy", StringUtils.defaultString(rs.getGuestName())));
        RsOrderResult rsOrderResult = createBatchOrder(reservationReq);
        return rsOrderResult;
    }


    public void pendingCheckOut(String hotelId, String orderNo) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, orderNo);
        if (rs == null) {
            throw new DefinedException("订单不存在", ResultCode.FORMERR.code());
        }
        // 当前预订为在住，离店日期为当天才能转待结账
        if (Objects.equals(rs.getReservationStatus(), StatusTypeUtils.RsStatus.CHECKIN) && CalculateDate.isEqual(rs.getDepartureDate(), new Date())) {

            rs.setReservationStatus(StatusTypeUtils.RsStatus.PENDING_CHECKOUT);
//        rs.setRoomNumber(String.format("P%s", rs.getRoomNumber()));
            daoLocal.merge(rs);

            TMultiRsdata olddata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(rs.getBlock()));
            olddata.fillArray(ProdType.ROOM.val(), Reservation.class, Arrays.asList(rs));

            TMultiRsdata newdata = new TMultiRsdata(true, StrUtil.EMPTY);
            List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);
            coreAvl.updateResourceAvailability(updList, hotelId, ProdType.ROOM.val());
            //待结账预订需要更新房间状态，修复缺陷对应的问题
            coreAvl.updRoomOccStatus(rs.getRoomNumber(), StrUtil.EMPTY, hotelId, true);

            String content = StrUtil.format("订单转待结账订单");
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                    rs.getReservationNumber(), content, GlobalContext.getCurrentUserId());
        } else {
            throw new DefinedException("在住预订只有当日离店才能转待结账订单", ResultCode.FORMERR.code());
        }
    }

    public Reservation cancelPending(String hotelId, String orderNo, String userId) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, orderNo);
        Reservation orgRs = PojoUtils.cloneEntity(rs);  //应用更改之前先做
        if (rs == null) {
            throw new DefinedException("订单不存在", ResultCode.FORMERR.code());
        }

        if (!CalculateDate.isEqual(rs.getDepartureDate(), new Date())) {
            throw new DefinedException("只能撤销当日离店订单", ResultCode.FORMERR.code());
        }

        // 检查之前的房间是否被占用
        checkRoomLocc(orgRs, hotelId);

        rs.setReservationStatus(StatusTypeUtils.RsStatus.CHECKIN);

        List<TSkuUpd> updList = checkUpdForm(rs, orgRs);  //恢复时检查房量
        if (CollectionUtil.isNotEmpty(updList)) {
            coreAvl.updateResourceAvailability(updList, hotelId, ProdType.ROOM.val());
        }

        List<RoomsDaily> dailyList = corePrice.writeRoomPrice(rs, false);
        coreDaily.updateRsDailys(rs, dailyList);

        String content = StrUtil.format("待结账订单撤销待结账恢复入住");
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                orgRs.getReservationNumber(), content, GlobalContext.getCurrentUserId());

        return rs;
    }

    public void cancelCheckout(String hotelId, String orderNo, String userId) throws DefinedException {
        Reservation rs = rsMapper.findByHotelIdAndReservationNumber(hotelId, orderNo);
        Reservation orgRs = PojoUtils.cloneEntity(rs);  //应用更改之前先做
        if (rs == null) {
            throw new DefinedException("订单不存在", ResultCode.FORMERR.code());
        }

        if (!CalculateDate.isEqual(rs.getDepartureDate(), new Date())) {
            throw new DefinedException("只能取消当日离店订单", ResultCode.FORMERR.code());
        }

        // 检查之前的房间是否被占用
        checkRoomLocc(orgRs, hotelId);

        rs.setReservationStatus(StatusTypeUtils.RsStatus.CHECKIN);

        List<TSkuUpd> updList = checkUpdForm(rs, orgRs);  //恢复时检查房量
        if (CollectionUtil.isNotEmpty(updList)) {
            coreAvl.updateResourceAvailability(updList, hotelId, ProdType.ROOM.val());
        }

        List<RoomsDaily> dailyList = corePrice.writeRoomPrice(rs, false);
        coreDaily.updateRsDailys(rs, dailyList);
    }

    private void checkRoomLocc(Reservation orgRs, String hotelId) throws DefinedException {
        Room room = roomMapper.findRoomByHotelIdAndRoomNo(hotelId, orgRs.getRoomNumber());
        if (room.getLocc() == 1) {
            throw new DefinedException(String.format("房间%s已被占用", orgRs.getRoomNumber()), ResultCode.FORMERR.code());
        }
    }

    /**
     * 将OTA入住人员信息转换为ProfileReq
     *
     * @param guestInfoList OTA入住人员信息列表
     * @return ProfileReq列表
     */
    private List<ProfileReq> convertGuestInfoToProfileReq(List<OtaGuestInfo> guestInfoList) {
        List<ProfileReq> profileReqList = new ArrayList<>();

        for (int i = 0; i < guestInfoList.size(); i++) {
            OtaGuestInfo guestInfo = guestInfoList.get(i);
            ProfileReq profileReq = new ProfileReq();

            profileReq.setGuestName(guestInfo.getGuestName());
            profileReq.setIdCard(guestInfo.getIdNumber());
            profileReq.setIdType(1); // 默认身份证

            // 从身份证号解析性别
            Integer gender = parseGenderFromIdCard(guestInfo.getIdNumber());
            profileReq.setGender(gender);

            // 设置主入住人（第一个或者明确标记的）
            if (i == 0 || guestInfo.isMain()) {
                profileReq.setMain(true);
            }

            profileReqList.add(profileReq);
        }

        return profileReqList;
    }

    /**
     * 从身份证号解析性别
     *
     * @param idCard 身份证号
     * @return 性别：0-女，1-男
     */
    private Integer parseGenderFromIdCard(String idCard) {
        if (StrUtil.isBlank(idCard) || idCard.length() < 17) {
            return 1; // 默认男性
        }

        try {
            // 18位身份证号的第17位（倒数第2位）表示性别，奇数为男，偶数为女
            char genderChar = idCard.charAt(16);
            int genderDigit = Character.getNumericValue(genderChar);
            return genderDigit % 2; // 奇数返回1（男），偶数返回0（女）
        } catch (Exception e) {
            return 1; // 解析失败默认男性
        }
    }

    /**
     * 构建OTA入住结果
     *
     * @param reservationList 预订列表
     * @param guestInfoList   入住人员信息列表
     * @param contactPhone    联系方式
     * @return OTA入住结果
     */
    private OtaCheckinResult buildOtaCheckinResult(List<Reservation> reservationList,
                                                   List<OtaGuestInfo> guestInfoList,
                                                   String contactPhone) {
        OtaCheckinResult result = new OtaCheckinResult();
        result.setRoomCount(reservationList.size());
        result.setContactPhone(contactPhone);

        // 构建房间信息列表
        List<OtaCheckinResult.OtaRoomInfo> roomInfoList = new ArrayList<>();
        for (Reservation reservation : reservationList) {
            OtaCheckinResult.OtaRoomInfo roomInfo = new OtaCheckinResult.OtaRoomInfo();
            roomInfo.setRoomNo(reservation.getRoomNumber());
            roomInfo.setRoomPassword(RandomUtil.randomNumbers(6) + ""); // 暂时为空，后续可以集成门锁系统  TODO  测试调试

            // 获取房型描述
            String roomTypeDesc = CustomData.getDesc(reservation.getHotelId(),
                    reservation.getRoomType(), SystemUtil.CustomDataKey.roomtype);
            roomInfo.setRoomTypeDesc(roomTypeDesc);

            roomInfoList.add(roomInfo);
        }
        result.setRooms(roomInfoList);

        // 构建入住人员信息列表
        List<OtaCheckinResult.OtaGuestResult> guestResultList = new ArrayList<>();
        for (OtaGuestInfo guestInfo : guestInfoList) {
            OtaCheckinResult.OtaGuestResult guestResult = new OtaCheckinResult.OtaGuestResult();
            guestResult.setGuestName(guestInfo.getGuestName());
            guestResult.setIdNumber(guestInfo.getIdNumber());
            guestResult.setCheckInStatus("已入住"); // 入住成功状态

            guestResultList.add(guestResult);
        }
        result.setGuests(guestResultList);

        return result;
    }

    public CheckinVerificationRes checkinVerification(CheckinVerificationReq req) {
        CheckinVerificationRes res = new CheckinVerificationRes();
        List<CheckinVerificationRes.CheckinVerificationsResult> results = new ArrayList<>();

        String hotelId = GlobalContext.getCurrentHotelId();
        List<Reservation> reservationList = rsMapper.findByReservationNumbers(req.getReservationNumbers(), GlobalContext.getCurrentHotelId());
        Map<String, Reservation> roomMap = reservationList.stream().collect(Collectors.toMap(Reservation::getRoomNumber, reservation -> reservation));
        // 按到店日期和离店日期区间把预订分组
        Map<String, List<Reservation>> groupedReservations = reservationList.stream()
                .collect(Collectors.groupingBy(reservation ->
                        reservation.getArrivalDate() + " - " + reservation.getDepartureDate()
                ));
        List<Reservation> nonOverlapReservations = new ArrayList<>();
        List<Reservation> occupiedReservationList = new ArrayList<>();
        for (Map.Entry<String, List<Reservation>> entry : groupedReservations.entrySet()) {
            String dateRange = entry.getKey(); // 获取日期区间
            String[] dates = dateRange.split(" - ");
            List<Reservation> reservations = entry.getValue(); // 获取该日期区间的预订列表
            List<String> roomNumbers = reservations.stream().map(Reservation::getRoomNumber).collect(Collectors.toList());

            List<Reservation> occupiedReservations = rsMapper.fetchRoomsOccupiedRs(roomNumbers, DateUtils.parseDate(dates[0]), DateUtils.parseDate(dates[1]), hotelId);

            for (Reservation rs : occupiedReservations) {
                CheckinVerificationRes.CheckinVerificationsResult checkinVerificationsResult = new CheckinVerificationRes.CheckinVerificationsResult();

                Reservation reservation =  roomMap.get(rs.getRoomNumber());
                occupiedReservationList.add(reservation);
                checkinVerificationsResult.setReservationNumber(reservation.getReservationNumber())
                                        .setResult(false)
                                        .setFailReason(StrUtil.format("房间号{}房间已被占用，无法入住", rs.getRoomNumber()))
                                        .setStatus(CheckinVerificationRes.VerificationStatus.NOT_ALLOWED)
                                        .setStatusDesc(CheckinVerificationRes.VerificationStatus.NOT_ALLOWED.getDesc());
                results.add(checkinVerificationsResult);
            }
            // 经过过滤还剩下的预订
            nonOverlapReservations.addAll(reservationList.stream()
                    .filter(rs -> occupiedReservationList.stream()
                            .noneMatch(occupied -> occupied.getReservationNumber().equals(rs.getReservationNumber())))
                    .collect(Collectors.toList()));
        }

        List<Optionswitch> optionswitchList = optionSwitchService.getOptionData(
                new OptionReq()
                        .setOption(Options.DIRTY_ROOMS_ALLOW_OCCUPANCY.name())
        );
        Optionswitch optionswitch = optionswitchList.get(0);
        List<String> roomNumberList = nonOverlapReservations.stream().map(Reservation::getRoomNumber).collect(Collectors.toList());

        // 酒店参数设置了脏房不允许入住
        if (!Boolean.valueOf(optionswitch.getSwitchstatus())) {
            String jpql = " from Room where hotelId=?1 and roomStatus=?2 and roomNo in (?3)";
            List<Room> roomList = daoLocal.getObjectList(jpql, hotelId, StatusTypeUtils.RoomStatus.DIRTY, roomNumberList);
            for (Room room : roomList) {
                CheckinVerificationRes.CheckinVerificationsResult checkinVerificationsResult = new CheckinVerificationRes.CheckinVerificationsResult();
                checkinVerificationsResult.setReservationNumber(roomMap.get(room.getRoomNo()).getReservationNumber())
                        .setResult(false)
                        .setFailReason(StrUtil.format("提醒客人，房间号{}房间是脏房", room.getRoomNo()))
                        .setStatus(CheckinVerificationRes.VerificationStatus.REMIND)
                        .setStatusDesc(CheckinVerificationRes.VerificationStatus.REMIND.getDesc());
                results.add(checkinVerificationsResult);
            }
        }
        res.setCheckinVerificationsResults(results);
        return res;
    }
}
