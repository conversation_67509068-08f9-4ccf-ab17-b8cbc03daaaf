package com.cw.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 包价详情表
 */
@Data
@Table(name = "pkgdet")
@Entity
public class Pkgdet implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;


    /**
     * 房价代码
     */
    @Column(name = "rate_code", length = 10, columnDefinition = " varchar(10)  default '' comment '房价代码' ")
    private String rateCode;

    /**
     * 酒店ID
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;


    /**
     * 开始时间
     */
    @Column(name = "startTime", columnDefinition = "datetime comment '开始时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "endTime", columnDefinition = "datetime comment '结束时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 房型代码
     */
    @Column(name = "room_type", columnDefinition = "LONGTEXT  comment '房型代码,多个以逗号分隔' ")
    private String roomType;


    /**
     * 包价代码
     */
    @Column(name = "include_code", columnDefinition = " varchar(500)  default '' comment '包价代码,多个以逗号分隔' ")
    private String includeCode; //3|BRF,1|WC,2|TK

}
