package com.cw.service.config.ota.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.core.CoreAvl;
import com.cw.core.CoreRs;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.Colrs;
import com.cw.entity.Reservation;
import com.cw.exception.DefinedException;
import com.cw.mapper.ColrsMapper;
import com.cw.mapper.ReservationMapper;
import com.cw.pms.request.crsv1.CwCrsColPayV1Req;
import com.cw.pms.request.crsv1.CwCrsColRsSaveV1Req;
import com.cw.pms.request.crsv1.CwCrsRoomRsSaveV1Req;
import com.cw.pms.response.crsv1.CwCrsColPayV1Res;
import com.cw.pms.response.crsv1.CwCrsColRsSaveV1Res;
import com.cw.pms.response.crsv1.CwCrsRoomSaveV1Res;
import com.cw.pojo.common.ResultCode;
import com.cw.service.context.OtaGlobalContext;
import com.cw.utils.CalculateDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * DSPMS 综合预订服务核心类
 * 处理预订平台发送的综合预订和客房预订请求
 *
 * <AUTHOR>
 * @Create 2025/7/22 02:46
 **/
@Slf4j
@Service
public class CwCrsApiService {

    private final CoreRs coreRs;
    private final CoreAvl coreAvl;
    private final ColrsMapper colrsMapper;
    private final ReservationMapper reservationMapper;

    @Autowired
    public CwCrsApiService(CoreRs coreRs, CoreAvl coreAvl, ColrsMapper colrsMapper, ReservationMapper reservationMapper) {
        this.coreRs = coreRs;
        this.coreAvl = coreAvl;
        this.colrsMapper = colrsMapper;
        this.reservationMapper = reservationMapper;
    }

    /**
     * 处理综合预订的新增或编辑
     * 根据请求中的 networkId 查询 colrs 表，判断是新建还是编辑综合预订
     * 如果是新增且包含客房信息，需要先检查房间可用性
     *
     * @param req 综合预订请求参数
     * @return 综合预订响应
     */
    public CwCrsColRsSaveV1Res saveDspmsColReservation(CwCrsColRsSaveV1Req req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        Colrs colrs = colrsMapper.findColrsByHotelIdAndCrsno(hotelId, req.getCrsColId());
        boolean lnew = colrs == null;


        StdOrderData orderData = new StdOrderData();
        if (lnew) {
            // 新增综合预订
            // 检查房间可用性
            // 创建订单
        } else {
            // 编辑综合预订
            // 更新订单
        }
        return null;
    }

    /**
     * 处理客房预订的新增或修改
     * 专门用于客房预订的简化接口，支持新增和修改本地订单
     * 参考 coreRs 中的代码实现
     *
     * @param req DSPMS客房预订请求参数
     * @return DSPMS客房预订响应
     */
    public CwCrsRoomSaveV1Res saveDspmsRoom(CwCrsRoomRsSaveV1Req req) {
        return null;
    }


    public CwCrsColPayV1Res crsColPayV1(CwCrsColPayV1Req req) {
        return null;
    }


}
