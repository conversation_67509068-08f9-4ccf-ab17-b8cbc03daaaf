package com.cw.service.config.pkgdet.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.cw.config.exception.BizException;
import com.cw.entity.Optionswitch;
import com.cw.entity.Pkgdet;
import com.cw.mapper.PkgdetMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.dto.pms.req.pkgdet.PkgdetReq;
import com.cw.pojo.dto.pms.req.pkgdet.QueryPkgdetReq;
import com.cw.pojo.dto.pms.req.pkgdet.UpdatePkgdetReq;
import com.cw.pojo.dto.pms.res.pkgdet.PkgdetListRes;
import com.cw.pojo.dto.pms.res.pkgdet.PkgdetRes;
import com.cw.service.config.options.OptionEntity;
import com.cw.service.config.pkgdet.PkgdetService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.JpaUtil;
import com.cw.utils.pages.PageResUtil;
import jodd.util.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 包价详情业务逻辑实现类
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Service
@Transactional
public class PkgdetServiceImpl implements PkgdetService {

    private static final Logger logger = LoggerFactory.getLogger(PkgdetServiceImpl.class);

    @Resource
    private PkgdetMapper pkgdetMapper;

    @Resource
    private DaoLocal<Pkgdet> daoLocal;

    private boolean isNew(PkgdetReq req) {
        return req.getId() == null || req.getId() <= 0;
    }


    public void save(PkgdetReq req) {
        if (isNew(req)) {
            addPkgdet(req);
        } else {
            updatePkgdet(req);
        }
    }

    private void addPkgdet(PkgdetReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        Pkgdet pkgdet = new Pkgdet();
        // 批量检查重复，减少重复查询
        List<String> duplicateRoomTypes = new ArrayList<>();

        String[] roomTypeArrays = req.getRoomType().split(",");
        List<String> inputRoomTypes = Arrays.asList(roomTypeArrays);
        extracted(inputRoomTypes, hotelId, roomTypeArrays, duplicateRoomTypes, null);

        // 如果有重复的房型，抛出异常（原有逻辑不变）
        if (!duplicateRoomTypes.isEmpty()) {
            throw new BizException(StrUtil.format("房型{}，已经配置有重复的包价详情。请检查后重新添加",
                    String.join(",", duplicateRoomTypes)
//                    CalculateDate.dateToString(req.getStartTime()),
//                    CalculateDate.dateToString(req.getEndTime()))
            ));
        }

        BeanUtil.copyProperties(req, pkgdet);
        pkgdet.setHotelId(hotelId);

        // 批量保存
        pkgdetMapper.save(pkgdet);

        logger.info("成功添加包价详情记录{}条，房价代码：{}，房型：{}",
                roomTypeArrays.length, req.getRateCode(), String.join(",", req.getRoomType()));
    }



    private void updatePkgdet(PkgdetReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();

        // 检查记录是否存在
        Optional<Pkgdet> optionalPkgdet = pkgdetMapper.findById(req.getId());
        if (!optionalPkgdet.isPresent()) {
            throw new BizException("包价详情记录不存在");
        }

        List<String> duplicateRoomTypes = new ArrayList<>();

        String[] roomTypeArrays = req.getRoomType().split(",");
        List<String> inputRoomTypes = Arrays.asList(roomTypeArrays);

        extracted(inputRoomTypes, hotelId, roomTypeArrays, duplicateRoomTypes, req.getId());

        if (!duplicateRoomTypes.isEmpty()) {
            throw new BizException(StrUtil.format("房型{}，已经配置有重复的包价详情。请检查后重新添加",
                    String.join(",", duplicateRoomTypes)
//                    CalculateDate.dateToString(req.getStartTime()),
//                    CalculateDate.dateToString(req.getEndTime()))
            ));
        }

        Pkgdet pkgdet = optionalPkgdet.get();
        BeanUtil.copyProperties(req, pkgdet);
        pkgdet.setHotelId(hotelId);
        pkgdetMapper.save(pkgdet);

        logger.info("成功修改包价详情记录，ID：{}，房价代码：{}，房型：{}",
                req.getId(), req.getRateCode(), req.getRoomType());
    }

    @Override
    public void deletePkgdet(Long id) {
        // 检查记录是否存在
        Optional<Pkgdet> optionalPkgdet = pkgdetMapper.findById(id);
        if (!optionalPkgdet.isPresent()) {
            throw new BizException("包价详情记录不存在");
        }

        pkgdetMapper.deleteById(id);
        logger.info("成功删除包价详情记录，ID：{}", id);
    }

    @Override
    public PkgdetListRes listPkgdet(QueryPkgdetReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        int currentPage = req.getPages().getQueryStartPage();
        int pageSize = req.getPages().getPagesize();

        // 使用JPA Criteria API构建查询条件
        Page<Pkgdet> pkgdetPage = pkgdetMapper.findAll((Root<Pkgdet> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 酒店ID条件（必须）
            predicates.add(cb.equal(root.get("hotelId"), hotelId));

            // 房价代码模糊查询
            if (StrUtil.isNotBlank(req.getRatecode())) {
                predicates.add(cb.like(root.get("ratecode"), "%" + req.getRatecode() + "%"));
            }

            // 房型代码模糊查询
            if (StrUtil.isNotBlank(req.getRoomtype())) {
                predicates.add(cb.like(root.get("roomtype"), "%" + req.getRoomtype() + "%"));
            }

            // 开始时间条件
            if (req.getStartTime() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("startTime"), req.getStartTime()));
            }

            // 结束时间条件
            if (req.getEndTime() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("endtime"), req.getEndTime()));
            }

            // 包价代码模糊查询
            if (StrUtil.isNotBlank(req.getIncludeCode())) {
                predicates.add(cb.like(root.get("includeCode"), "%" + req.getIncludeCode() + "%"));
            }

            // 有效时间查询（时间范围内的记录）
            if (req.getValidTime() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("startTime"), req.getValidTime()));
                predicates.add(cb.greaterThanOrEqualTo(root.get("endtime"), req.getValidTime()));
            }

            // 将所有条件组合为AND关系
            Predicate[] predicateArray = new Predicate[predicates.size()];
            query.where(cb.and(predicates.toArray(predicateArray)));
            return query.getRestriction();
        }, PageRequest.of(currentPage, pageSize, Sort.by(req.getPages().getDirection(),
                req.getPages().getSortname().split(","))));

        // 转换为响应对象
        PkgdetListRes res = new PkgdetListRes();
        res.fillPageData(pkgdetPage);
        return res;
    }

    @Override
    public PkgdetRes findPkgdet(Long id) {
        Optional<Pkgdet> optionalPkgdet = pkgdetMapper.findById(id);
        if (!optionalPkgdet.isPresent()) {
            throw new BizException("包价详情记录不存在");
        }

        Pkgdet pkgdet = optionalPkgdet.get();
        PkgdetRes pkgdetRes = new PkgdetRes();
        BeanUtil.copyProperties(pkgdet, pkgdetRes);

        return pkgdetRes;
    }

    // 新增参数：excludeId（待排除的记录ID，更新时传入当前记录ID）
    private void extracted(List<String> inputRoomTypes, String hotelId, String[] roomTypeArrays,
                                   List<String> duplicateRoomTypes, Long excludeId) {
        List<Pkgdet> existingRoomTypeStrs = null;
        if (!inputRoomTypes.isEmpty()) {
            Specification<Pkgdet> spec = (root, query, cb) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("hotelId"), hotelId));

                // 新增：排除指定ID（更新时传入当前记录ID，避免与自身冲突）
                if (excludeId != null && excludeId > 0) {
                    predicates.add(cb.notEqual(root.get("id"), excludeId));
                }

                // 动态添加房型检查条件（原有逻辑不变）
                List<Predicate> roomTypePredicates = new ArrayList<>();
                for (String roomType : roomTypeArrays) {
                    Expression<Integer> findInSet = cb.function(
                            "FIND_IN_SET", Integer.class, cb.literal(roomType), root.get("roomType")
                    );
                    roomTypePredicates.add(cb.greaterThan(findInSet, 0));
                }
                predicates.add(cb.or(roomTypePredicates.toArray(new Predicate[0])));

                query.select(root.get("roomType")).distinct(true);
                return cb.and(predicates.toArray(new Predicate[0]));
            };

            existingRoomTypeStrs = pkgdetMapper.findAll(spec);

            // 解析查询结果（原有逻辑不变）
            Set<String> existingRoomTypes = new HashSet<>();
            for (Pkgdet roomTypeStr : existingRoomTypeStrs) {
                if (roomTypeStr != null) {
                    existingRoomTypes.addAll(Arrays.asList(roomTypeStr.getRoomType().split(",")));
                }
            }
            existingRoomTypes.retainAll(inputRoomTypes);
            duplicateRoomTypes.addAll(existingRoomTypes);
        }
    }
}
