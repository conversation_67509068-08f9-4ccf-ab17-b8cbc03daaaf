package com.cw.service.config.pkgdet;

import com.cw.pojo.dto.pms.req.pkgdet.PkgdetReq;
import com.cw.pojo.dto.pms.req.pkgdet.QueryPkgdetReq;
import com.cw.pojo.dto.pms.req.pkgdet.UpdatePkgdetReq;
import com.cw.pojo.dto.pms.res.pkgdet.PkgdetListRes;
import com.cw.pojo.dto.pms.res.pkgdet.PkgdetRes;

/**
 * 包价详情业务逻辑接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface PkgdetService {

    /**
     * 新增包价详情记录
     * 支持按照多个房型进行添加，防止重复添加（hotelid + ratecode + 房型代码为维度）
     *
     * @param pkgdetReq 包价详情请求对象
     */
//    void addPkgdet(PkgdetReq pkgdetReq);
//
//    /**
//     * 修改包价详情记录（单条记录进行修改）
//     *
//     * @param updatePkgdetReq 修改包价详情请求对象
//     */
//    void updatePkgdet(UpdatePkgdetReq updatePkgdetReq);

    /**
     * 删除包价详情记录
     *
     * @param id 记录ID
     */
    void deletePkgdet(Long id);

    /**
     * 列表查询包价详情
     *
     * @param queryPkgdetReq 查询包价详情请求对象
     * @return 包价详情列表响应
     */
    PkgdetListRes listPkgdet(QueryPkgdetReq queryPkgdetReq);

    /**
     * 根据ID查看单个包价详情记录
     *
     * @param id 记录ID
     * @return 包价详情响应对象
     */
    PkgdetRes findPkgdet(Long id);

    /**
     * 保存包价详情记录
     * @param req
     */
    void save(PkgdetReq req);
}
