package com.cw.service.config.reservation.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.PojoUtils;
import com.cw.arithmetic.SysFunLibTool;
import com.cw.arithmetic.others.CodeDetail;
import com.cw.arithmetic.others.GuestCheckinProfileProcessor;
import com.cw.arithmetic.sku.OpskuPickup;
import com.cw.arithmetic.sku.TMultiRsdata;
import com.cw.arithmetic.sku.TSkuUpd;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.OptionSwitchTool;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.config.exception.BizException;
import com.cw.config.exception.CustomException;
import com.cw.core.*;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.*;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.common.core.MainRsHeader;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.PageReq;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.common.res.PriceNode;
import com.cw.pojo.dto.pms.req.profile.ProfileReq;
import com.cw.pojo.dto.pms.req.rate.QueryRoomRateDetailReq;
import com.cw.pojo.dto.pms.req.reservation.*;
import com.cw.pojo.dto.pms.req.standardgroup.QueryStandardGroupReq;
import com.cw.pojo.dto.pms.req.standardgroup.StandardGroupReq;
import com.cw.pojo.dto.pms.res.profile.ProfileRes;
import com.cw.pojo.dto.pms.res.rate.RoomRateDetailRes;
import com.cw.pojo.dto.pms.res.reservation.*;
import com.cw.pojo.dto.pms.res.standardgroup.StandardGroupRes;
import com.cw.service.config.profile.ProfileService;
import com.cw.service.config.rate.RoomRateDetailService;
import com.cw.service.config.reservation.ReservationService;
import com.cw.service.config.standardgroup.StandardGroupService;
import com.cw.service.context.GlobalContext;
import com.cw.service.log.UserLogService;
import com.cw.utils.*;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.StatusTypeUtils;
import com.cw.utils.jpa.DynamicSpecificationBuilder;
import com.cw.utils.jpa.Operator;
import com.cw.utils.jpa.SearchCriteria;
import com.cw.utils.options.Options;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname ReservationServiceImpl
 * @Description 预定信息接口实现
 * @Date 2024-03-27 20:44
 * <AUTHOR> sancho.shen
 */
@Slf4j
@Service
public class ReservationServiceImpl implements ReservationService {

    @Resource
    private ReservationMapper reservationMapper;

    @Resource
    private ResrvationHisMapper resrvationHisMapper;

    @Resource
    private ProfileMapper profileMapper;

    @Resource
    private ColrsMapper colrsMapper;

    @Resource
    private RoomRateDetailService roomRateDetailService;

    @Resource
    private ProfileService profileService;

    @Resource
    private UserLogService userLogService;

    @Resource
    private StandardGroupService standardGroupService;

    @Resource
    private RoomMapper roomMapper;
    @Resource
    private CoreRs coreRs;

    @Resource
    private GuestCheckinProfileProcessor guestCheckinProfileProcessor;

    @Resource
    private CoreAvl coreAvl;

    @Resource
    private CorePrice corePrice;

    @Resource
    private CoreDaily coreDaily;

    @Resource
    private SeqNoService seqNoService;

    @Resource
    private DaoLocal<?> dao;

    /**
     * @Description: 创建散客预订
     * @MethodName: addReservationTourist
     * @Author: sancho
     * @Date: 2024-06-08 23:48
     * @param: reservationReq
     * @return: com.cw.pojo.dto.pms.res.reservation.RsOrderResult
     */
    @Override
    public RsOrderResult addReservationTourist(ReservationReq reservationReq) throws DefinedException {

        RsOrderResult orderResult = coreRs.createBatchOrder(reservationReq);

/*        Reservation reservation = new Reservation();
        // 将预订请求的属性复制到预订对象中
        BeanUtil.copyProperties(reservationReq, reservation);
        // 设置预订对象的酒店ID属性
        reservation.setHotelId(GlobalContext.getCurrentHotelId());
        // 生成预订号
        String reservationNumber = IdUtil.generateId();
        reservation.setReservationNumber(reservationNumber);
        //  计算生成每日房价信息记录
        List<RoomsDaily> dailyList=   corePrice.writeRoomPrice(reservation,false);
        // 生成一个预订关联号
        reservation.setRelationNumber("P" + cn.hutool.core.util.IdUtil.getSnowflakeNextIdStr());

        Reservation dbRservation=JpaUtil.appendEntity(reservation);

        TMultiRsdata olddata = new TMultiRsdata(true,StrUtil.EMPTY);
        // 保存预订对象到数据库中
        reservationMapper.save(dbRservation);

        //更新相关表统计.并记录房价信息
        TMultiRsdata newdata = new TMultiRsdata(false, reservation.getBlock());
        newdata.fillArray(ProdType.ROOM.val(),Reservation.class,Collections.singletonList(reservation));
        List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);
        coreAvl.updateResourceAvailability(updList,dbRservation.getHotelId(),ProdType.ROOM.val());
        coreDaily.updateRsDailys(dbRservation,dailyList);

        String content = StrUtil.format("创建预定，预定号：{}", reservation.getReservationNumber());
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, GlobalContext.getCurrentUserId(),
                reservation.getReservationNumber(), content, GlobalContext.getCurrentHotelId());
        RsOrderResult orderResult = new RsOrderResult();
        orderResult.setActualAmount(BigDecimal.valueOf(0.01));
        orderResult.setTotalAmount(BigDecimal.valueOf(0.01));  //先返回一个固定的金额 方便前端测试
        orderResult.setReservaitonNumber(reservationNumber);*/


        return orderResult;
    }

    @Override
    public RsOrderResult addGroupReservation(BatchCreateRsReq reservationReq) throws DefinedException {
        RsOrderResult orderResult = coreRs.createBatchOrder(reservationReq);
        return orderResult;
    }


    @Override
    public void updateReservation(UpdateReservationReq updateReservationReq) throws DefinedException {
        Long id = updateReservationReq.getId();
        Optional<Reservation> data = reservationMapper.findById(id);
        if (!data.isPresent()) {
            throw new BizException(StrUtil.format("找不到对应的id为{}的预订数据", id));
        }
        Reservation orgReservation = PojoUtils.cloneEntity(data.get());
        Reservation dbReservation = data.get();  //JpaUtil.appendEntity(data, updateReservationReq);
        String arrivalDate = updateReservationReq.getArrivalDate();
        String departureDate = updateReservationReq.getDepartureDate();
        if (StrUtil.isNotEmpty(arrivalDate) && StrUtil.isNotEmpty(departureDate)) {
            int roomNight = (int) DateUtil.betweenDay(DateUtil.parse(arrivalDate), DateUtil.parse(departureDate), true);
            if (roomNight != orgReservation.getRoomNight()) {
                dbReservation.setRoomNight(roomNight);
            }
        }
//        BeanUtil.copyProperties(updateReservationReq, dbReservation, CopyOptions.create().ignoreNullValue());
        // 排除预订状态的变更
        BeanUtil.copyProperties(
                updateReservationReq
                , dbReservation
                , CopyOptions.create().ignoreNullValue().setIgnoreProperties("reservationStatus"));
        if (!dbReservation.getRoomType().equals(orgReservation.getRoomType()) && StrUtil.isNotBlank(orgReservation.getRoomNumber())) {
            dbReservation.setRoomNumber(StrUtil.EMPTY);
        }
        //TODO  校验修改请求参数是否合法
        TMultiRsdata olddata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(orgReservation.getBlock()));
        olddata.fillArray(ProdType.ROOM.val(), Reservation.class, Collections.singletonList(orgReservation));
        TMultiRsdata newdata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(dbReservation.getBlock()));
        newdata.fillArray(ProdType.ROOM.val(), Reservation.class, Collections.singletonList(dbReservation));
        List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);

        //如果校验通过.就进行更新
        coreRs.checkUpdReservation(orgReservation, dbReservation, updList);

        List<RoomsDaily> dailyList = corePrice.writeRoomPrice(dbReservation, false);//计算最新房价
        coreRs.updBuildingNo(dbReservation);//换房的话把楼号给更新掉

        reservationMapper.save(dbReservation);

        coreAvl.updateResourceAvailability(updList, dbReservation.getHotelId(), ProdType.ROOM.val());
        coreDaily.updateRsDailys(dbReservation, dailyList);

        //updateRoomStatus(orgReservation.getRoomNumber(), dbReservation.getRoomNumber(), dbReservation.getReservationStatus(),```````````````````````````````````````````````````

        //        dbReservation.getArrivalDate(), dbReservation.getDepartureDate());

        coreAvl.updRoomOccStatus(orgReservation.getRoomNumber(), dbReservation.getRoomNumber(), dbReservation.getHotelId(), false);


        //修改变更记录信息汇总 存入
        BeanChangeUtil<Reservation> t = new BeanChangeUtil<>();
        String changeInfo = t.contrastObj(orgReservation, dbReservation, dbReservation.getHotelId());
        if (StringUtils.isNotBlank(changeInfo)) {
            String content = "[修改预定:" + data.get().getReservationNumber() + "]\n" + changeInfo;
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                    data.get().getReservationNumber(), content, GlobalContext.getCurrentHotelId());
        }
    }

    @Override
    public void checkin(CheckinReq checkInReq) {
        String hotelId = GlobalContext.getCurrentHotelId();
        List<String> regList = checkInReq.getCheckinInfoList();

//        List<Reservation> reservationList = reservationMapper.findByReservationNumbers(regList, hotelId);
        String jpql = "select r from Reservation r where r.reservationNumber in(?1)  and hotelId=?2 ";
        List<Reservation> reservationList = dao.getObjectList(jpql, regList, hotelId);
        List<Reservation> checkinReservationList = new ArrayList<>();
        Date hotelDate = CalculateDate.getSystemDate();

        // 第一步：筛选可入住的预订并更新状态
        for (Reservation reservation : reservationList) {
            if (reservation.getReservationStatus() == StatusTypeUtils.RsStatus.EXPECTED
                    && (CalculateDate.isEqual(reservation.getArrivalDate(), hotelDate)
                    || CalculateDate.isEqual(CalculateDate.reckonDay(reservation.getArrivalDate(), 5, 1), hotelDate))
                    && StrUtil.isNotBlank(reservation.getRoomNumber())) {

                reservation.setReservationStatus(StatusTypeUtils.RsStatus.CHECKIN);
                reservation.setModifiedBy(GlobalContext.getCurrentUserId());
                reservation.setModifiedTime(new Date());
                checkinReservationList.add(reservation);
            }
        }

        if (CollUtil.isEmpty(checkinReservationList)) {
            throw new BizException("没有可入住的预订");
        }

        // 第二步：批量保存预订状态
        try {
            for(Reservation reservation : checkinReservationList) {
                dao.merge(reservation);
            }
//            reservationMapper.saveAll(checkinReservationList);
        } catch (Exception e) {
            throw new BizException("保存入住状态失败: " + e.getMessage());
        }

        // 第三步：批量更新房间状态
        try {
            for (Reservation reservation : checkinReservationList) {
                boolean inRange = CalculateDate.isInRange(CalculateDate.getSystemDate(),
                        reservation.getArrivalDate(), reservation.getDepartureDate());

                if (inRange && OptionSwitchTool.getOptionSatus(Options.CI_AUTODI, hotelId)) {
                    roomMapper.updateRoomStatusDirty(reservation.getRoomType(),
                            reservation.getRoomNumber(), reservation.getHotelId(),
                            new Date(), GlobalContext.getCurrentUserId());
                }
                roomMapper.updateRoomLocc(reservation.getRoomNumber(), reservation.getHotelId());
            }
        } catch (Exception e) {
            throw new BizException("更新房间状态失败: " + e.getMessage());
        }

        // 第四步：记录日志
        for (Reservation reservation : checkinReservationList) {
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY,
                    GlobalContext.getCurrentUserId(), reservation.getReservationNumber(),
                    StrUtil.format("办理入住 {} {}", reservation.getRoomNumber(), reservation.getGuestName()), hotelId);
        }
    }

    @Override
    public void cancel(CancelReq cancelReq) throws DefinedException {
        coreRs.cancelOrder(GlobalContext.getCurrentHotelId(), cancelReq.getReservationNumber());
        String content = StrUtil.format("取消订单");
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                cancelReq.getReservationNumber(), content, GlobalContext.getCurrentUserId());
    }

    @Override
    public void cancelCheckin(CancelReq cancelReq) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();
        String userId = GlobalContext.getCurrentUserId();
        coreRs.cancelCheckIn(hotelId, cancelReq.getReservationNumber(), userId);
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, userId,
                cancelReq.getReservationNumber(), "取消入住", hotelId);
    }

    @Override
    public void roomMove(RoomMoveReq roomMoveReq) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();

        UpdRsParamForm form = roomMoveReq.getUpdRsParamForm();


        coreRs.changeRsParaSave(form, hotelId, GlobalContext.getCurrentUserId()); //分房,换房,延住.都统一调用这个方法.会进行占用,以及目标客房可卖数校验

        // 办理入住
        String content = StrUtil.format("换房，预定关联编号：{},房间号：{}，房型代码：{}，房价代码：{}，换房原因：{}",
                roomMoveReq.getReservationNumber(), roomMoveReq.getRoomNumber(), roomMoveReq.getRoomType(), roomMoveReq.getRateCode(),
                roomMoveReq.getReason());
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                roomMoveReq.getReservationNumber(), content, hotelId);

    }

    @Override
    public void checkout(CheckoutReq checkoutReq) throws DefinedException {
        coreRs.checkOut(GlobalContext.getCurrentHotelId(), checkoutReq.getReservationNumber(), GlobalContext.getCurrentUserId()); //JUST 2024.10.28 封装到core 层.跟自助机共用调用
    }

    @Override
    public Common_response batchCheckout(BatchCheckoutReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        int successCount = 0;
        int failCount = 0;
        for (String regNo : req.getRegNos()) {
            try {
                coreRs.checkOut(hotelId, regNo, GlobalContext.getCurrentUserId());
                successCount++;
            } catch (DefinedException e) {
                failCount++;
            }
        }
        Common_response response = new Common_response();
        response.setMsg(StrUtil.format("批量退房成功:{}，失败:{}", successCount, failCount));
        return response;
    }

    @Override
    public void addShare(AddShareReq addShareReq) {
        String hotelId = GlobalContext.getCurrentHotelId();
        // 创建条件列表
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("reservationNumber", addShareReq.getReservationNumber(), Operator.EQUAL));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", hotelId, Operator.EQUAL));
        // 查询预订信息
        Reservation reservation = findReservation(conditionList);

        List<Accompany> accompanyList = new ArrayList<>();
        // 查询原陪同人员
        String accompanyOld = reservation.getAccompany();
        if (CharSequenceUtil.isNotEmpty(accompanyOld)) {
            // 转换
            accompanyList.addAll(JSONUtil.parseArray(accompanyOld).toList(Accompany.class));
        }
        // 获取陪同人员信息
        List<ProfileReq> profileReqList = addShareReq.getAccompanyList();
        for (ProfileReq profileReq : profileReqList) {
            // 入档案库
            ProfileRes profileRes = profileService.addProfile(profileReq, GlobalContext.getCurrentHotelId());
            // 其他的当做陪同人员
            Accompany accompany = new Accompany();
            accompany.setGender(profileReq.getGender());
            accompany.setIdCard(profileReq.getIdCard());
            accompany.setProfileNumber(profileRes.getProfileNumber());
            accompany.setGuestName(profileReq.getGuestName());
            accompanyList.add(accompany);
        }
        reservation.setAccompany(JSONUtil.toJsonStr(accompanyList));

        reservation.setModifiedBy(GlobalContext.getCurrentUserId());
        reservation.setModifiedTime(new Date());
        reservation.setPersonTotal(accompanyList.size() + 1);
        reservationMapper.save(reservation);
        List<String> accompanyNames = accompanyList.stream().map(node -> node.getGuestName() + "," + node.getIdCard()).collect(Collectors.toList());
        String content = StrUtil.format("添加同住人，预定关联编号：{}，同住人信息：{}", addShareReq.getReservationNumber(),
                JSON.toJSONString(accompanyNames));
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                addShareReq.getReservationNumber(), content, hotelId);
    }

    @Override
    public void extend(ExtendReq extendReq) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();


        UpdRsParamForm form = extendReq.getUpdRsParamForm();

        Reservation newRs = coreRs.changeRsParaSave(form, hotelId, GlobalContext.getCurrentUserId());


/*        // 创建条件列表
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("reservationNumber", extendReq.getReservationNumber(), Operator.EQUAL));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", hotelId, Operator.EQUAL));
        // 查询预订信息
        Reservation reservation = findReservation(conditionList);

        Reservation orgReservation = PojoUtils.cloneEntity(reservation);

        List<RoomsDaily> dailyList = corePrice.writeRoomPrice(reservation, false);//计算最新房价

        reservation.setDepartureDate(extendReq.getDepartureDate());
        reservation.setModifiedBy(GlobalContext.getCurrentUserId());
        reservation.setModifiedTime(new Date());
        reservationMapper.save(reservation);*/


        String content = StrUtil.format("延住，预定关联编号：{}，离店日期：{}", extendReq.getReservationNumber(), CalculateDate.dateToString(extendReq.getDepartureDate()));
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                extendReq.getReservationNumber(), content, hotelId);
    }

    @Override
    public RateQueryRes rateQuery(RateQueryReq rateQueryReq) {

        RateQueryRes rateQueryRes = new RateQueryRes();
        if (StrUtil.isBlankIfStr(rateQueryReq.getRateCode())) {
            return rateQueryRes;
        }

        String hotelId = GlobalContext.getCurrentHotelId();
        RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);

        List<RoomType> roomTypes = roomTypeCache.getDataList(hotelId);
        List<String> roomTypeCodes = roomTypes.stream().map(node -> node.getRoomType()).collect(Collectors.toList());
        String buildingNo = GlobalContext.getCurrentBuildingNo();
        if (StrUtil.isNotBlank(buildingNo)) {
            roomTypes = roomTypes.stream().filter(r -> r.getBuildingNo().equals(buildingNo)).collect(Collectors.toList());
        }


        List<RateQueryRes.RoomProductDetailListData> ls = new ArrayList<>();

        List<CodeDetail> codeDetails = corePrice.queryAvlPriceGrid(rateQueryReq.getRateCode(), Joiner.on(",").join(roomTypeCodes), hotelId, rateQueryReq.getFromDate(), rateQueryReq.getEndDate());
        HashMap<String, Integer> avlInfo = coreAvl.getResourceLeft_DB(roomTypeCodes, ProdType.ROOM.val(), StrUtil.EMPTY, rateQueryReq.getFromDate(), rateQueryReq.getEndDate(), 1, 1, hotelId);

        //        CoreAvl coreAvl=SpringUtil.getBean(CoreAvl.class);
        //coreAvl.getResourceLeft_DB(calcProducts,ProdType.ROOM.val(),StrUtil.EMPTY,startDate,endDate,1,1);

        for (CodeDetail codeDetail : codeDetails) {//目前的设计效果是单选房价码.所以可以这么弄
            for (RoomType roomType : roomTypes) {
                RateQueryRes.RoomProductDetailListData data = new RateQueryRes.RoomProductDetailListData();
                data.setRoomType(roomType.getRoomType());
                data.setDesc(roomType.getDescription());
                data.setAvl(avlInfo.getOrDefault(roomType.getRoomType(), 0));//TODO 查询缓存获取可卖房数 与缓存的价格
                data.setPriceInfo(SysFunLibTool.getShowPrice(codeDetail.getProductPrice(roomType.getRoomType())));

                if (NumberUtil.isLess(codeDetail.getProductPrice(roomType.getRoomType()), BigDecimal.ZERO)) {
                    data.setAvl(0);
                }

                data.setPicurl(roomType.getPicture());
                ls.add(data);
            }
            //Collections.sort(ls, new Comparator<RateQueryRes.RoomProductDetailListData>() {
            //    @Override
            //    public int compare(RateQueryRes.RoomProductDetailListData o1, RateQueryRes.RoomProductDetailListData o2) {
            //        if (o1.getAvl() == 0 && o2.getAvl() != 0) {
            //            return 1; // o1 should come after o2
            //        } else if (o1.getAvl() != 0 && o2.getAvl() == 0) {
            //            return -1; // o1 should come before o2
            //        }
            //        return 1;
            //    }
            //});
            Collections.sort(ls, new Comparator<RateQueryRes.RoomProductDetailListData>() {
                @Override
                public int compare(RateQueryRes.RoomProductDetailListData o1, RateQueryRes.RoomProductDetailListData o2) {
                    // 首先比较可用性状态：非0的排在前面
                    if (o1.getAvl() == 0 && o2.getAvl() == 0) {
                        return 0;  // 两者都是0，认为相等
                    }
                    if (o1.getAvl() == 0) {
                        return 1;  // o1是0，应该排在后面
                    }
                    if (o2.getAvl() == 0) {
                        return -1; // o2是0，o1应该排在前面
                    }
                    // 都不是0的情况，直接比较数值大小（降序）
                    return Integer.compare(o2.getAvl(), o1.getAvl());
                }
            });
        }


        rateQueryRes.setRecords(ls);
        return rateQueryRes;
    }


    @Override
    public AssignReservationRes assignRoom(AssignRoomReq assignRoomReq) {
        AssignReservationRes assignReservationRes = new AssignReservationRes();
        String reservationNumber = assignRoomReq.getReservationNumber();
        String groupNumber = assignRoomReq.getGroupNumber();
        if (CharSequenceUtil.isNotEmpty(reservationNumber)) {
            // 拆分散客预订
            assignReservationRes.setReservationNumberList(assignTouristRoom(assignRoomReq));
        } else if (CharSequenceUtil.isNotEmpty(groupNumber)) {
            // 拆分团队预订
            //assignReservationRes.setReservationNumberList(assignGroupRoom(assignRoomReq));
        } else {
            throw new BizException("预订号/团队号不允许同时为空");
        }
        return assignReservationRes;
    }

    @Override
    public BatchAssginRoomResult batchAssignRoom(BatchAssignRoomReq req) throws DefinedException {
        BatchAssginRoomResult result = coreRs.batchAssignRoomReq(req, GlobalContext.getCurrentHotelId());
        return result;
    }


    @Override
    public void registration(RegistrationReq registrationReq) {
        List<String> reservationNumberList = registrationReq.getReservationNumberList();
        int size = reservationNumberList.size();
        // 入住人员信息
        List<ProfileReq> profileReqList = registrationReq.getProfileReqList();
        // 是否陪同
        boolean shared = registrationReq.isShared();

        List<Accompany> accompanyList = new ArrayList<>();
        // 假如是同住
        if (shared) {//追加陪同人员
            // 查询预订
            Reservation reservation = findReservation(reservationNumberList.get(0));
            // 查询原陪同人员
            String accompanys = reservation.getAccompany();
            for (ProfileReq profileReq : profileReqList) {
                // 入档案库
                ProfileRes profileRes = profileService.addProfile(profileReq, GlobalContext.getCurrentHotelId());
                // 其他的当做陪同人员
                Accompany accompany = new Accompany();
                accompany.setGender(profileReq.getGender());
                accompany.setIdCard(profileReq.getIdCard());
                accompany.setProfileNumber(profileRes.getProfileNumber());
                accompany.setGuestName(profileReq.getGuestName());
                accompanyList.add(accompany);
            }
            // 追加
            if (CharSequenceUtil.isNotEmpty(accompanys)) {
                // 转换
                accompanyList.addAll(JSONUtil.parseArray(accompanys).toList(Accompany.class));
            }

            reservation.setAccompany(JSONUtil.toJsonStr(accompanyList));
            reservation.setModifiedTime(new Date());
            reservation.setCreateBy(GlobalContext.getCurrentUserId());
            reservation.setPersonTotal(accompanyList.size() + 1);
            // 修改预订
            dao.merge(reservation);
        } else {
            // 使用Spring管理的Bean，确保事务一致性
            guestCheckinProfileProcessor.processInfo(registrationReq.getProfileReqList(),
                    registrationReq.getReservationNumberList(), GlobalContext.getCurrentHotelId());

        /*    // 是否有主预订人
            boolean hasMain = false;
            for (ProfileReq profileReq : profileReqList) {
                if (profileReq.isMain()) {
                    hasMain = true;
                    break;
                }
            }
            // 自动分配人员
            List<List<ProfileReq>> splitList = CollUtil.split(profileReqList, size);//TODO  处理前端请求有问题
            for (int i = 0; i < splitList.size(); i++) {
                // 修改预订
                String reservationNumber = reservationNumberList.get(i);
                // 先查询预定
                Reservation reservation = findReservation(reservationNumber);

                List<ProfileReq> profileReqs = splitList.get(i);
                String profileNumber = "";
                String guestName = reservation.getGuestName();
                for (int j = 0; j < profileReqs.size(); j++) {
                    ProfileReq profileReq = profileReqs.get(j);
                    // 入档案库
                    ProfileRes profileRes = profileService.addProfile(profileReq);
                    // 其他的当做陪同人员
                    Accompany accompany = new Accompany();
                    accompany.setGender(profileRes.getGender());
                    accompany.setIdCard(profileReq.getIdCard());
                    accompany.setProfileNumber(profileRes.getProfileNumber());
                    accompany.setGuestName(profileRes.getGuestName());

                    if (hasMain) {
                        // 假如是主预订人
                        if (profileReq.isMain()) {
                            // 假如是主预订人
                            profileNumber = profileRes.getProfileNumber();
                            guestName = profileRes.getGuestName();
                        } else {
                            accompanyList.add(accompany);
                        }
                    } else {
                        //否则使用第一个人做主入住人
                        // 第一个人做主入住人
                        if (j == 0) {
                            profileNumber = profileRes.getProfileNumber();
                            guestName = profileRes.getGuestName();
                        } else {
                            accompanyList.add(accompany);
                        }
                    }
                }
                // 修改预订
                reservation.setProfileNumber(profileNumber);
                reservation.setAccompany(JSON.toJSONString(accompanyList));
                reservation.setGuestName(guestName);
                reservation.setModifiedTime(new Date());
                reservation.setCreateBy(GlobalContext.getCurrentUserId());
                reservation.setPersonTotal(accompanyList.size() + 1);
                reservationMapper.save(reservation);*/
        }
        reservationMapper.flush();
    }

    @Override
    public List<ProfileRes> findProfile(String reservationNumber) {
        // 查询预定
        Reservation reservation = findReservation(reservationNumber);
        String profileNumber = reservation.getProfileNumber();
        String accompanys = reservation.getAccompany();
        List<String> profileNumberList = new ArrayList<>();
        profileNumberList.add(profileNumber);
        if (CharSequenceUtil.isNotEmpty(accompanys)) {
            List<Accompany> list = JSONUtil.parseArray(accompanys).toList(Accompany.class);
            for (Accompany accompany : list) {
                // 添加陪同人员的档案号
                profileNumberList.add(accompany.getProfileNumber());
            }
        }
        // 查询档案

        return profileService.listProfileByNumber(profileNumberList);
    }


    @Override
    public RoomRateDetailRes queryRoomRate(QueryRateReq queryRateReq) {
        QueryRoomRateDetailReq queryRoomRateDetailReq = new QueryRoomRateDetailReq();
        queryRoomRateDetailReq.setRoomType(queryRateReq.getRoomType());
        queryRoomRateDetailReq.setRateCode(queryRateReq.getRateCode());
        return roomRateDetailService.findRoomRateDetail(queryRoomRateDetailReq);
    }

    @Override
    public ReservationListRes listReservationPage(QueryReservationListReq queryReservationListReq) {
        // 创建条件列表
        List<SearchCriteria> conditionList = getConditionList(queryReservationListReq);
        Specification<Reservation> querySpecification = DynamicSpecificationBuilder.getQuerySpecification(Reservation.class, conditionList);

        if (StrUtil.isNotBlank(queryReservationListReq.getSearchKey())) {
            querySpecification = querySpecification.and((root, query, cb) -> {
                String searchPattern = "%" + queryReservationListReq.getSearchKey().trim() + "%";
                // 构建guestName的like条件
                Predicate guestNameLike = cb.like(root.get("guestName"), searchPattern);

                // 构建roomNumber的相等条件
                // 注意：这里假设roomNumber是String类型，如果是其他类型需要进行相应转换
                Predicate roomNumberEquals = cb.equal(root.get("roomNumber"), queryReservationListReq.getSearchKey().trim());
                // 组合两个条件为OR关系
                // 添加排序条件
                query.orderBy(cb.desc(root.get("createTime")));
                return cb.or(guestNameLike, roomNumberEquals);
            });
        }

        Page<Reservation> pages = reservationMapper.findAll(querySpecification, JpaUtil.getPageRequest(queryReservationListReq.getPages()));

        ////筛选今日预抵或今日预离预定
//        if (queryReservationListReq.isTodayArrive() || queryReservationListReq.isTodayLeave()) {
//            pages = getTodayArrOrDepReservations(querySpecification, queryReservationListReq);
//        } else {
//            pages = reservationMapper.findAll(querySpecification, JpaUtil.getPageRequest(queryReservationListReq.getPages()));
//        }
        ReservationListRes res = new ReservationListRes();
        res.fillPageData(pages);
        List<ReservationRes> reservationResList = res.getRecords();
        for (ReservationRes reservationRes : reservationResList) {
            reservationRes.setReservationStatusDesc(StatusTypeUtils.getRsStatusDesc(reservationRes.getReservationStatus()));
        }
        //PageResUtil.fillPagesDate(pages, ReservationListRes.class);

        Map<String, SystemUtil.CustomDataKey> transinfo = Maps.newHashMap();
        transinfo.put("roomType", SystemUtil.CustomDataKey.roomtype);  //用房型描述字段,覆盖查询结果
        transinfo.put("channel", SystemUtil.CustomDataKey.channel);  //把渠道的描述转出来
        transinfo.put("hotel", SystemUtil.CustomDataKey.building);  //将楼栋的描述转出来

        // 映射转换
        Map<String, String> mappings = new HashMap<>();
        mappings.put("roomType", "roomTypeDesc");
        CustomData.transTableData(GlobalContext.getCurrentHotelId(), res.getRecords(), transinfo, mappings, true);

        //应离未离订单状态转为4
        //if (CollectionUtil.isNotEmpty(res.getRecords())) {
        //    for (ReservationRes node : res.getRecords()) {
        //        //所有状态列表 判断入住状态离店时间 应离未离标识
        //        if (queryReservationListReq.getReservationStatus() == null &&
        //                StatusTypeUtils.RsStatus.CHECKIN == node.getReservationStatus() && node.getDepartureDate().before(DateUtil.beginOfDay(new Date()))) {
        //            node.setReservationStatus(StatusTypeUtils.RsStatus.NOT_DEPARTED);
        //        } else if (queryReservationListReq.getReservationStatus() != null &&
        //                StatusTypeUtils.RsStatus.NOT_DEPARTED == queryReservationListReq.getReservationStatus()) {
        //            //查询应离未离状态转换
        //            node.setReservationStatus(StatusTypeUtils.RsStatus.NOT_DEPARTED);
        //        }
        //    }
        //}
        return res;
    }

    @Override
    public ReservationListRes queryTransRes(QueryRsTransferPageReq queryReservationListReq) {
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        conditionList.add(new SearchCriteria("reservationStatus", Arrays.asList(StatusTypeUtils.RsStatus.CHECKIN), Operator.IN));//2.1暂时先只开放转账到入住预订
        conditionList.add(new SearchCriteria("reservationNumber", queryReservationListReq.getExcludeRsno(), Operator.NOT_EQUAL));
        conditionList.add(new SearchCriteria("guestName", queryReservationListReq.getSearchKey(), Operator.LIKE));
        // 排除已离店预订
        conditionList.add(new SearchCriteria("departureDate", DateUtil.date(), Operator.GREATER_THAN_EQUAL));

        Specification<Reservation> querySpecification = DynamicSpecificationBuilder.getQuerySpecification(Reservation.class, conditionList);
        Page<Reservation> pages = reservationMapper.findAll(querySpecification, JpaUtil.getPageRequest(queryReservationListReq.getPages()));
//        List<ReservationRes> reservationResList = pages.getContent().stream().map(
//                row -> {
//                    ReservationRes node = BeanUtil.toBean(row, ReservationRes.class);
//                    node.setReservationStatusDesc(StatusTypeUtils.getRsStatusDesc((row.getReservationStatus())));
//                    return node;
//                }
//        ).collect(Collectors.toList());

        ReservationListRes res = new ReservationListRes();
        res.fillPageData(pages);
        List<ReservationRes> reservationResList = res.getRecords();
        for (ReservationRes reservationRes : reservationResList) {
            reservationRes.setReservationStatusDesc(StatusTypeUtils.getRsStatusDesc(reservationRes.getReservationStatus()));
        }
        //PageResUtil.fillPagesDate(pages, ReservationListRes.class);
        Map<String, SystemUtil.CustomDataKey> transinfo = Maps.newHashMap();
        transinfo.put("roomType", SystemUtil.CustomDataKey.roomtype);  //用房型描述字段,覆盖查询结果
        transinfo.put("channel", SystemUtil.CustomDataKey.channel);  //把渠道的描述转出来
        transinfo.put("hotel", SystemUtil.CustomDataKey.building);  //将楼栋的描述转出来

        // 映射转换
        Map<String, String> mappings = new HashMap<>();
        mappings.put("roomType", "roomTypeDesc");
        CustomData.transTableData(GlobalContext.getCurrentHotelId(), res.getRecords(), transinfo, mappings, true);

        return res;
    }


    @Override
    public void cancelReservation(Common_Load_Req req) throws DefinedException {
        coreRs.cancelOrder(GlobalContext.getCurrentHotelId(), req.getResNo());
    }

    @Override
    public ReservationRes queryRoomRs(QueryReservationReq req) {
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        String hotelId = GlobalContext.getCurrentHotelId();
        Reservation rs = daoLocal.getObject("from Reservation where hotelId=?1 and roomNumber=?2 and reservationStatus=?3", hotelId, req.getRoomNumber(), 1);
        ReservationRes result = new ReservationRes();
        if (rs != null) {
            List<AccompanyInfo> info = JSON.parseArray(rs.getAccompany(), AccompanyInfo.class);
            if (CollectionUtil.isNotEmpty(info)) {
                for (AccompanyInfo accompanyInfo : info) {
                    if (accompanyInfo.getIdCard().endsWith(req.getIdCard())) {
                        BeanUtil.copyProperties(rs, result);
                        return result;
                    }
                }
            } else {
                Profile profile = profileMapper.findByHotelIdAndProfileNumber(hotelId, rs.getProfileNumber());
                if (profile != null && profile.getIdCard().endsWith(req.getIdCard())) {
                    BeanUtil.copyProperties(rs, result);
                    return result;
                }
            }
        }
        return result;
    }

    @Override
    public List<ReservationRes> listReservation(String relationNumber) {
        // 假如预订关联号为空则返回空数据
        if (StrUtil.isEmpty(relationNumber)) {
            return new ArrayList<>();
        }
        String hotelId = GlobalContext.getCurrentHotelId();

        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("relationNumber", relationNumber, Operator.EQUAL));
        conditionList.add(new SearchCriteria("hotelId", hotelId, Operator.EQUAL));
        Specification<Reservation> querySpecification = DynamicSpecificationBuilder.getQuerySpecification(Reservation.class, conditionList);
        List<Reservation> all = reservationMapper.findAll(querySpecification);
        // 转换
        List<ReservationRes> result = BeanUtil.copyToList(all, ReservationRes.class);
        Map<String, SystemUtil.CustomDataKey> transinfo = Maps.newHashMap();
        transinfo.put("roomType", SystemUtil.CustomDataKey.roomtype);  //用房型描述字段,覆盖查询结果
        transinfo.put("channel", SystemUtil.CustomDataKey.channel);  //把渠道的描述转出来

        // 映射转换
        Map<String, String> mappings = new HashMap<>();
        mappings.put("roomType", "roomTypeDesc");
        CustomData.transTableData(hotelId, result, transinfo, mappings, false);
        return result;
    }

    @Override
    public List<ReservationRes> queryReservation(QueryReservationReq queryReservationReq) {
        // 创建条件列表
        List<SearchCriteria> conditionList = new ArrayList<>();
        String hotelId = GlobalContext.getCurrentHotelId();

        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", hotelId, Operator.EQUAL));

        // 预订编号
        if (StrUtil.isNotBlank(queryReservationReq.getReservationNumber())) {
            conditionList.add(new SearchCriteria("reservationNumber", queryReservationReq.getReservationNumber(), Operator.EQUAL));
        }
        // 手机号码
        if (StrUtil.isNotBlank(queryReservationReq.getTelephone())) {
            conditionList.add(new SearchCriteria("telephone", queryReservationReq.getTelephone(), Operator.EQUAL));

        }
        // 预订状态
        if (queryReservationReq.getReservationStatus() != null) {
            conditionList.add(new SearchCriteria("reservationStatus", queryReservationReq.getReservationStatus(), Operator.EQUAL));
        }

        // 房间号码
        if (StrUtil.isNotBlank(queryReservationReq.getRoomNumber())) {
            conditionList.add(new SearchCriteria("roomNumber", queryReservationReq.getRoomNumber(), Operator.EQUAL));
        }
        // 查询预订信息
        Specification<Reservation> querySpecification = DynamicSpecificationBuilder.getQuerySpecification(Reservation.class, conditionList);

        List<Reservation> all = reservationMapper.findAll(querySpecification);
        List<ReservationRes> result = BeanUtil.copyToList(all, ReservationRes.class);
        Map<String, SystemUtil.CustomDataKey> transinfo = Maps.newHashMap();
        transinfo.put("roomType", SystemUtil.CustomDataKey.roomtype);  //用房型描述字段,覆盖查询结果
        transinfo.put("channel", SystemUtil.CustomDataKey.channel);  //把渠道的描述转出来

        // 映射转换
        Map<String, String> mappings = new HashMap<>();
        mappings.put("roomType", "roomTypeDesc");
        CustomData.transTableData(hotelId, result, transinfo, mappings, false);
        return result;
    }

    @Override
    public RoomRsViewRes loadRoomRsView(RoomRsLoadReq req) {
        RoomRsViewRes res = new RoomRsViewRes();
        String hotelId = GlobalContext.getCurrentHotelId();
        RoomRsViewRes.RoomRs roomRs = new RoomRsViewRes.RoomRs();
        boolean lrsMode = req.getMode() == null || req.getMode() == 0;
        if (lrsMode) {
            Reservation reservation = reservationMapper.findByHotelIdAndReservationNumber(hotelId, req.getReservationNumber());
            if (reservation == null) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("找不到对应的预订信息"));
            }
            List<RoomsDaily> roomsDaily = coreDaily.getDailys(req.getReservationNumber(), hotelId);
            List<PriceNode> nodes = roomsDaily.stream().map(r -> new PriceNode(r.getDatum(), r.getPrice())).collect(Collectors.toList());


            res.setMainRoomRs(roomRs);
            res.setRateinfo(nodes);
            roomRs.setRoomTypeDesc(CustomData.getDesc(hotelId, reservation.getRoomType(), SystemUtil.CustomDataKey.roomtype));
            roomRs.setSalerDesc(CustomData.getDesc(hotelId, reservation.getSalerid(), SystemUtil.CustomDataKey.saler));
            if (StrUtil.isNotBlank(reservation.getBuildingNo())) {
                roomRs.setBuildingDesc(CustomData.getDesc(hotelId, reservation.getBuildingNo(), SystemUtil.CustomDataKey.building));
            }


            BeanUtil.copyProperties(reservation, roomRs);
            BigDecimal totalPrice = roomsDaily.stream().map(r -> r.getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
            roomRs.setTotalPrice(totalPrice.multiply(BigDecimal.valueOf(roomRs.getRooms())));
            //加载团队或者散客团的关联预订
            if (StrUtil.isNotBlank(reservation.getRelationNumber())) {
                List<Reservation> reservations = reservationMapper.findMyLinkRs(reservation.getRelationNumber(), reservation.getReservationNumber(), hotelId);
                List<RoomRsViewRes.RoomRs> roomRsList = reservations.stream().map(r -> {
                    RoomRsViewRes.RoomRs other = new RoomRsViewRes.RoomRs();
                    BeanUtil.copyProperties(r, other);
                    other.setRoomTypeDesc(CustomData.getDesc(hotelId, r.getRoomType(), SystemUtil.CustomDataKey.roomtype));
                    return other;
                }).collect(Collectors.toList());
                res.setOthers(roomRsList);

                if (!reservation.getRelationNumber().startsWith("P")) {
                    Colrs colrs = dao.getObject("from Colrs where hotelId=?1 and bookingid=?2", hotelId, roomRs.getRelationNumber());
                    MainRsHeader header = new MainRsHeader();
                    header.fillColRsInfo(colrs);
                    res.setMainRsHeader(header);
                }

            }
        } else {
            ReservationHis reservationHis = resrvationHisMapper.findByHotelIdAndReservationNumber(hotelId, req.getReservationNumber());
            BeanUtil.copyProperties(reservationHis, roomRs);
        }

        res.setMainRoomRs(roomRs);


        return res;
    }

    public Reservation findReservation(String reservationNumber) {
        // 预订编号
        if (StrUtil.isBlank(reservationNumber)) {
            throw new BizException("预订编号不能为空");
        }
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("reservationNumber", reservationNumber, Operator.EQUAL));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        return findReservation(conditionList);
    }

    private Reservation findReservation(List<SearchCriteria> conditionList) {
        Specification<Reservation> querySpecification = DynamicSpecificationBuilder.getQuerySpecification(Reservation.class, conditionList);
        Optional<Reservation> one = reservationMapper.findOne(querySpecification);

        Reservation reservation = null;
        if (one.isPresent()) {
            reservation = one.get();
        }
        if (reservation == null) {
            throw new BizException("未找到符合的预订信息");
        }
        return reservation;
    }

    /**
     * 根据查询预订请求获取条件列表
     *
     * @param queryReservationListReq 查询预订请求
     * @return 条件列表
     */
    private List<SearchCriteria> getConditionList(QueryReservationListReq queryReservationListReq) {

        // 创建条件列表
        List<SearchCriteria> conditionList = new ArrayList<>();
        // 证件号码
        if (StrUtil.isNotBlank(queryReservationListReq.getIdCard())) {
            conditionList.add(new SearchCriteria("idCard", queryReservationListReq.getIdCard(), Operator.LIKE));
        }
        // 房间号码
        if (StrUtil.isNotBlank(queryReservationListReq.getRoomNumber())) {
            conditionList.add(new SearchCriteria("roomNumber", queryReservationListReq.getRoomNumber(), Operator.EQUAL));
        }
        // 添加房型编码条件
        if (StrUtil.isNotBlank(queryReservationListReq.getRoomType())) {
            conditionList.add(new SearchCriteria("roomType", queryReservationListReq.getRoomType(), Operator.EQUAL));
        }
        // 添加客人姓名条件
        if (StrUtil.isNotBlank(queryReservationListReq.getGuestName())) {
            conditionList.add(new SearchCriteria("guestName", queryReservationListReq.getGuestName(), Operator.LIKE));
        }
        // 添加预订编码条件
        if (StrUtil.isNotBlank(queryReservationListReq.getReservationNumber())) {
            conditionList.add(new SearchCriteria("reservationNumber", queryReservationListReq.getReservationNumber(), Operator.LIKE));
        }
        // 添加号码查询条件
        if (StrUtil.isNotBlank(queryReservationListReq.getTelephone())) {
            conditionList.add(new SearchCriteria("telephone", queryReservationListReq.getTelephone(), Operator.EQUAL));
        }
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        // 添加入住时间条件
        if (queryReservationListReq.getArrivalDate() != null) {
            conditionList.add(new SearchCriteria("arrivalDate", queryReservationListReq.getArrivalDate(), Operator.GREATER_THAN_EQUAL));
        }
        // 离店日期
        if (queryReservationListReq.getDepartureDate() != null) {
            conditionList.add(new SearchCriteria("departureDate", queryReservationListReq.getDepartureDate(), Operator.LESS_THAN_EQUAL));
        }
        // 添加档案编号条件
        if (queryReservationListReq.getProfileNumber() != null) {
            conditionList.add(new SearchCriteria("profileNumber", queryReservationListReq.getProfileNumber(), Operator.EQUAL));
        }

        // 房价码
        if (StrUtil.isNotBlank(queryReservationListReq.getRateCode())) {
            conditionList.add(new SearchCriteria("rateCode", queryReservationListReq.getRateCode(), Operator.EQUAL));
        }

        if (queryReservationListReq.isTodayArrive()) {
            conditionList.add(new SearchCriteria("arrivalDate", DateUtil.beginOfDay(new Date()), Operator.EQUAL));
            queryReservationListReq.setReservationStatus(StatusTypeUtils.RsStatus.EXPECTED);
        }

        if (queryReservationListReq.isTodayLeave()) {
            conditionList.add(new SearchCriteria("departureDate", DateUtil.beginOfDay(new Date()), Operator.EQUAL));
            queryReservationListReq.setReservationStatus(StatusTypeUtils.RsStatus.CHECKIN);
        }

        // 预定状态
        if (queryReservationListReq.getReservationStatus() != null) {
            if (queryReservationListReq.getReservationStatus() != StatusTypeUtils.RsStatus.NOT_DEPARTED) {
                conditionList.add(new SearchCriteria("reservationStatus", queryReservationListReq.getReservationStatus(), Operator.EQUAL));
                if (!queryReservationListReq.isTodayLeave() && queryReservationListReq.getReservationStatus() == StatusTypeUtils.RsStatus.CHECKIN) {
                    //在住订单排除预离和应离未离  日期大于当天
                    conditionList.add(new SearchCriteria("departureDate", DateUtil.beginOfDay(new Date()), Operator.GREATER_THAN_EQUAL));
                }
            } else {
                //查询应离未离
                conditionList.add(new SearchCriteria("reservationStatus", StatusTypeUtils.RsStatus.CHECKIN, Operator.EQUAL));
                conditionList.add(new SearchCriteria("departureDate", DateUtil.beginOfDay(new Date()), Operator.LESS_THAN));
            }
        }

        // 渠道来源
        if (StrUtil.isNotBlank(queryReservationListReq.getChannel())) {
            conditionList.add(new SearchCriteria("channel", queryReservationListReq.getChannel(), Operator.EQUAL));
        }

        if (queryReservationListReq.getAssignRoomMode() > 0) {//指定是否已经分房.给分房页面用的.
            conditionList.add(new SearchCriteria("roomNumber", "", queryReservationListReq.getAssignRoomMode() == 1 ? Operator.EQUAL : Operator.NOT_EQUAL));
        }

        if (queryReservationListReq.getGroupMode() > 0) {//指定是否已经分房.给分房页面用的.

            String colrsSql = "select bookingid from colrs where hotelid='" + GlobalContext.getCurrentHotelId() + "'";
            List<String> bookingIdList = dao.getNativeObjectList(colrsSql);
            if (queryReservationListReq.getGroupMode() == 2) {
                if (CollectionUtil.isNotEmpty(bookingIdList)) {
                    conditionList.add(new SearchCriteria("relationNumber", bookingIdList, Operator.IN));
                }
            } else if (queryReservationListReq.getGroupMode() == 1) {
                conditionList.add(new SearchCriteria("relationNumber", bookingIdList, Operator.NOT_IN));
            }
        }

        if (StrUtil.isNotBlank(queryReservationListReq.getResNo())) {
                conditionList.add(new SearchCriteria("reservationNumber", queryReservationListReq.getResNo(), Operator.EQUAL));
        }

        if (StrUtil.isNotBlank(queryReservationListReq.getOtaResNo())) {
            conditionList.add(new SearchCriteria("otano", queryReservationListReq.getOtaResNo(), Operator.EQUAL));
        }

        if (StrUtil.isNotBlank(queryReservationListReq.getOtaResNo())) {
            conditionList.add(new SearchCriteria("otano", queryReservationListReq.getOtaResNo(), Operator.EQUAL));
        }

        if (StrUtil.isNotBlank(queryReservationListReq.getGroupname())) {
            conditionList.add(new SearchCriteria("groupname", queryReservationListReq.getGroupname(), Operator.EQUAL));
        }

        if (StrUtil.isNotBlank(queryReservationListReq.getGroupNo())) {
            conditionList.add(new SearchCriteria("relationNumber", queryReservationListReq.getGroupNo(), Operator.EQUAL));
        }

        // 按登录用户所属楼栋或者传入楼栋筛选数据，用户所属楼栋优先级高
        String buildingNo = StrUtil.isNotBlank(GlobalContext.getCurrentBuildingNo())
                ? GlobalContext.getCurrentBuildingNo()
                : queryReservationListReq.getBuildingNo();
        if (StrUtil.isNotBlank(buildingNo)) {
            conditionList.add(new SearchCriteria("buildingNo", buildingNo, Operator.EQUAL));
        }

        return conditionList;
    }

    @Override
    public void deleteReservation(Long id) {
        Optional<Reservation> data = reservationMapper.findById(id);
        if (!data.isPresent()) {
            throw new BizException(StrUtil.format("找不到对应的id为{}的预订数据", id));
        }
        reservationMapper.deleteById(id);
        //TODO 删除订单是否需要将对应的房间状态恢复为非占用的干净房状态？
        Reservation reservation = data.get();
        //此处只处理当前的房间状态，未来的房间预定不影响当前的房间状态
        boolean inRange = CalculateDate.isInRange(CalculateDate.getSystemDate(), reservation.getArrivalDate(), reservation.getDepartureDate());
        if (inRange) {
            roomMapper.updateRoomLoccAndDirty(StatusTypeUtils.RoomLocc.UN_OCCUPY, StatusTypeUtils.RoomStatus.CLEAN, new Date(),
                    GlobalContext.getCurrentUserId(), reservation.getRoomNumber(), reservation.getHotelId());
        }

        String content = StrUtil.format("删除预定，预定号：{}", reservation.getReservationNumber());
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, GlobalContext.getCurrentUserId(),
                reservation.getReservationNumber(), content, GlobalContext.getCurrentHotelId());

    }

    private Reservation updateOldReservation(AssignRoomInfo assignRoomInfo, Reservation reservation) {
        if (reservation == null) {
            reservation = new Reservation();
            reservation.setCreateBy(GlobalContext.getCurrentUserId());
            reservation.setCreateTime(new Date());
        }
        reservation.setRoomNumber(assignRoomInfo.getRoomNumber());
        // 修改原预订为订1间房
        reservation.setRooms(1);

        try {
            corePrice.writeRoomPrice(reservation, false);//主要是为了修复totalprice 不正确
        } catch (DefinedException e) {
            throw new RuntimeException(e);
        }


        return reservation;
    }

    /**
     * @Description:分房拆分预订
     * @MethodName: splitReservation
     * @Author: sancho
     * @Date: 2024-06-08 23:15
     * @param: assignRoomInfoList
     * @param: oldReservation
     * @return: java.util.List<com.cw.entity.Reservation>
     */
    private List<Reservation> splitReservation(List<AssignRoomInfo> assignRoomInfoList, Reservation oldReservation) {
        // 拆分预订

        List<Reservation> createReservationList = new ArrayList<>();

        for (AssignRoomInfo assignRoomInfo : assignRoomInfoList) {


            // 开始拆分订单// 即生成多个预订
            Reservation createReservation = PojoUtils.cloneEntity(oldReservation, false);//2024.10.12 JUST 修改.从主预订复制过来
            // 待入住
            createReservation.setReservationStatus(StatusTypeUtils.RsStatus.EXPECTED);
            createReservation.setRooms(1);
            createReservation.setHotelId(oldReservation.getHotelId());
            createReservation.setRoomNumber(assignRoomInfo.getRoomNumber());
            createReservation.setReservationNumber(seqNoService.getSequenceID(SystemUtil.SequenceKey.ORDERID));
            createReservation.setBalance(BigDecimal.ZERO);
            createReservation.setConsume(BigDecimal.ZERO);
            createReservation.setDeposit(BigDecimal.ZERO);
            createReservation = JpaUtil.appendEntity(createReservation);
            coreDaily.copyRsDailys(oldReservation.getArrivalDate(), oldReservation.getReservationNumber(),
                    createReservation.getReservationNumber(), createReservation.getHotelId(), createReservation);

            //从主预订复制每日房价 JUST

            /*// 设置预订对象的房价
            createReservation.setTotalPrice(assignRoomInfo.getPrice());
            // 设置预订对象的房价代码
            createReservation.setRateCode(assignRoomInfo.getRateCode());
            // 设置预订对象的房型属性
            createReservation.setRoomType(assignRoomInfo.getRoomType());
            // 设置预订对象的酒店ID属性
            createReservation.setHotelId(GlobalContext.getCurrentHotelId());
            // 生成预订号

            // 预订关联号
            createReservation.setRelationNumber(oldReservation.getRelationNumber());
            createReservation.setChannel(oldReservation.getChannel());
            // 房间号

            // 客人名
            createReservation.setGuestName(oldReservation.getGuestName());

            createReservation.setRooms(1);//房间号改为1
            // 渠道
            createReservation.setChannel(oldReservation.getChannel());
            // 预订状态
            createReservation.setReservationStatus(oldReservation.getReservationStatus());

            // 到店时间
            createReservation.setArrivalDate(oldReservation.getArrivalDate());
            // 离店时间
            createReservation.setDepartureDate(oldReservation.getDepartureDate());
            // 联系人电话
            createReservation.setTelephone(oldReservation.getTelephone());
            // 拼接其他信息
            createReservation = JpaUtil.appendEntity(createReservation);*/
            // 保存预订对象到数据库中
            createReservationList.add(createReservation);
        }
        return createReservationList;
    }

    private List<AccompanyInfo> getAccompanyInfoList(List<ProfileReq> profileReqList) {
        if (CollUtil.isNotEmpty(profileReqList)) {
            // 假如入住人数大于1,则获取同住人信息
            if (profileReqList.size() > 1) {
                List<AccompanyInfo> accompanyList = new ArrayList<>();
                for (int i = 0; i < profileReqList.size(); i++) {
                    if (i == 0) {
                        // 排除掉一个,剩下都是同住人
                        continue;
                    }
                    ProfileReq profileReq = profileReqList.get(i);
                    AccompanyInfo accompanyInfo = new AccompanyInfo();
                    accompanyInfo.setGuestName(profileReq.getGuestName());
                    accompanyInfo.setGender(profileReq.getGender());
                    accompanyInfo.setIdCard(profileReq.getIdCard());
                    accompanyInfo.setIdType(profileReq.getIdType());
                    accompanyList.add(accompanyInfo);
                }
                return accompanyList;
            }
            // 否则就没有同住人
            return null;
        }
        return null;
    }

    /**
     * @Description: 拆分散客订单
     * @MethodName: assignTouristRoom
     * @Author: sancho
     * @Date: 2024-06-08 17:40
     * @param: assignRoomReq
     */
    private List<String> assignTouristRoom(AssignRoomReq assignRoomReq) {
        List<String> reservationNumberList = new ArrayList<>();
        String reservationNumber = assignRoomReq.getReservationNumber();
        // 通过预订编号查询预订信息
        Reservation reservation = findReservation(reservationNumber);
        // 获取购买的房间数量
        int rooms = reservation.getRooms();
        if (rooms <= 0) {
            throw new BizException(StrUtil.format("预订号{}的房间数量不正确", reservationNumber));
        }

        // 分配的房间信息集合
        List<AssignRoomInfo> assignRoomInfoList = assignRoomReq.getAssignRoomInfoList();
        // 判断分配的房间数量是否大于预订的房间数量
        int size = assignRoomInfoList.size();
        if (size > rooms) {
            throw new BizException("分配的房间数量不能大于预订的房间数量");
        }

        if (rooms == 1) {
            // 直接修改原预订的房间信息
            reservationMapper.save(updateOldReservation(assignRoomInfoList.get(0), reservation));
            reservationNumberList.add(reservationNumber);
            //分配房间之后，需要更新房间状态，防止该房间会被二次选中
            //此处只处理当前的房间状态，未来的房间预定不影响当前的房间状态
            boolean inRange = CalculateDate.isInRange(CalculateDate.getSystemDate(), reservation.getArrivalDate(), reservation.getDepartureDate());
            if (inRange) {
                roomMapper.updateRoomLocc(reservation.getRoomNumber(), reservation.getHotelId());
            }
        } else {
            Reservation oldReservation = null;

            // 否则需要分房
            if (size == rooms) {
                // 假如需要分配的房间数量等于购买的数量,则需要留出一间房分配给原预订
                oldReservation = updateOldReservation(assignRoomInfoList.remove(0), reservation);
            }
            // 拆分订单,若分房数量与预订房间数量相等，则此时assignRoomInfoList已被提前移除了一个元素，后续分配给原始预订
            List<Reservation> createReservationList = splitReservation(assignRoomInfoList, reservation);
            if (oldReservation != null) {
                // 拼上原来的预订
                createReservationList.add(oldReservation);
            }
            if (StrUtil.isBlank(oldReservation.getRelationNumber())) {
                String relationNumber = "P" + cn.hutool.core.util.IdUtil.getSnowflakeNextIdStr();
                for (Reservation rs : createReservationList) {
                    rs.setRelationNumber(relationNumber);
                }
            }

            // 保存预订对象到数据库中
            reservationMapper.saveAll(createReservationList);
            for (Reservation reservation1 : createReservationList) {
                reservationNumberList.add(reservation1.getReservationNumber());
                //此处只处理当前的房间状态，未来的房间预定不影响当前的房间状态
                boolean inRange1 = CalculateDate.isInRange(CalculateDate.getSystemDate(), reservation1.getArrivalDate(), reservation1.getDepartureDate());
                //分配房间之后，需要更新房间状态，防止该房间会被二次选中
                if (inRange1) {
                    roomMapper.updateRoomLocc(reservation1.getRoomNumber(), reservation1.getHotelId());
                }
            }
        }

        // 假如准备分配的房间数量小于原预订的房间数量,则说明原预订还有房没有完全分配，此时只需要修改原预定的房间数量即可
        // 等待下次分配
        if (size < rooms) {
            // 原预订的房间数量
            reservation.setRooms(rooms - assignRoomInfoList.size());
            try {
                corePrice.writeRoomPrice(reservation, false);  //修改房间数会影响总价
            } catch (DefinedException e) {
                e.printStackTrace();
            }
            reservationMapper.save(reservation);
        }
        return reservationNumberList;
    }

    /**
     * @Description: 拆分团队订单
     * @MethodName: assignGroupRoom
     * @Author: sancho
     * @Date: 2024-06-08 17:41
     * @param: assignRoomReq
     */
   /* private List<String> assignGroupRoom(AssignRoomReq assignRoomReq) {
        String groupNumber = assignRoomReq.getGroupNumber();
        if (CharSequenceUtil.isEmpty(groupNumber)) {
            throw new BizException("团队号不能为空");
        }
        //1.通过团队号查询团队预订信息
        QueryStandardGroupReq queryStandardGroupReq = new QueryStandardGroupReq();
        //queryStandardGroupReq.setGroupNumber(groupNumber);
        StandardGroupRes standardGroupRes = standardGroupService.findStandardGroup(queryStandardGroupReq);
        if (standardGroupRes == null) {
            throw new BizException("找不到相应的团队预订信息");
        }

        String relationNumber = "S" + cn.hutool.core.util.IdUtil.getSnowflakeNextIdStr();
        List<AssignRoomInfo> assignRoomInfoList = assignRoomReq.getAssignRoomInfoList();

        // 构造预订对象
        Reservation reservationReq = new Reservation();
        reservationReq.setRelationNumber(relationNumber);
        reservationReq.setArrivalDate(standardGroupRes.getArrivalDate());
        reservationReq.setDepartureDate(standardGroupRes.getDepartureDate());
        reservationReq.setGuestName(standardGroupRes.getContacter());

        // 拆分订单
        List<Reservation> createReservationList = splitReservation(assignRoomInfoList, reservationReq);
        // 保存预订对象到数据库中
        reservationMapper.saveAll(createReservationList);
        List<String> reservationNumberList = new ArrayList<>();
        for (Reservation reservation : createReservationList) {
            reservationNumberList.add(reservation.getReservationNumber());
            //分配房间之后，需要更新房间状态，防止该房间会被二次选中
            //此处只处理当前的房间状态，未来的房间预定不影响当前的房间状态
            boolean inRange = CalculateDate.isInRange(CalculateDate.getSystemDate(), reservation.getArrivalDate(), reservation.getDepartureDate());
            if (inRange) {
                roomMapper.updateRoomLocc(reservation.getRoomNumber(), reservation.getHotelId());
            }
        }
        return reservationNumberList;
    }*/


    /**
     * 今日预抵订单=订单状态为0-预定状态，且入住日期和当前日期对比小于一天
     *
     * @param reservations 当天的客人预定数据
     * @return 预抵或预离的房间数量
     */
    private List<Reservation> getTodayArrivalReservations(List<Reservation> reservations) {
        // 根据条件筛选出预抵或预离的预订
        return reservations.stream()
                .filter(reservation -> {
                    Date dateTime = reservation.getArrivalDate();
                    return dateTime != null &&
                            reservation.getReservationStatus() == StatusTypeUtils.RsStatus.EXPECTED &&
                            CalculateDate.isEqual(dateTime, CalculateDate.getSystemDate());
                })
                .collect(Collectors.toList());
    }

    /**
     * 今日预离订单=订单状态为1-在住状态，且离店日期和当前日期对比小于一天
     *
     * @param reservations 当天的客人预定数据
     * @return 预抵或预离的房间数量
     */
    private List<Reservation> getTodayDepReservations(List<Reservation> reservations) {
        // 根据条件筛选出预抵或预离的预订
        return reservations.stream()
                .filter(reservation -> {
                    Date dateTime = reservation.getDepartureDate();
                    return dateTime != null &&
                            reservation.getReservationStatus() == StatusTypeUtils.RsStatus.CHECKIN &&
                            DateUtil.betweenDay(dateTime, new Date(), true) < 1;
                })
                .collect(Collectors.toList());
    }

    /**
     * 筛选今日预抵或今日预离的订单
     *
     * @param querySpecification      查询条件
     * @param queryReservationListReq 请求参数
     * @return 今日预抵或今日预离的订单
     */
    private Page<Reservation> getTodayArrOrDepReservations(Specification<Reservation> querySpecification, QueryReservationListReq queryReservationListReq) {
        boolean isTodayArrive = queryReservationListReq.isTodayArrive();
        boolean isTodayLeave = queryReservationListReq.isTodayLeave();
        //筛选今日预抵
        List<Reservation> arrOrDepRoom = Lists.newArrayList();
        //筛选今日预抵或今日预离预定
        if (isTodayArrive || isTodayLeave) {
            List<Reservation> reservations = reservationMapper.findAll(querySpecification);
            if (isTodayArrive) {
                //今日预抵=订单状态为0，预定状态，且入住日期和当前日期对比小于一天
                arrOrDepRoom = getTodayArrivalReservations(reservations);
            } else {
                //今日预离=订单状态为1，在住状态，且离店日期和当前日期对比小于一天
                arrOrDepRoom = getTodayDepReservations(reservations);
            }
        }
        PageReq.PageData pages = queryReservationListReq.getPages();
        arrOrDepRoom.sort(Comparator.comparing(Reservation::getId));
        //查询当前页数
        int currentPage = pages.getCurrentpage();
        //查询的每页大小
        int pageSize = pages.getPagesize();
        //符合条件的总数
        int total = arrOrDepRoom.size();
        //根据前端请求的分页数据，截取相应数据返回给前端
        int start = (currentPage - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        return new PageImpl<>(arrOrDepRoom.subList(start, end), JpaUtil.getPageRequest(pages), total);
    }

    /*    *//**
     * 2024.10.24 不建议继续调用.单个预订无法决定房间当日是否还会有预订占用
     * 根据预定的状态，更新对应房间占用情况和房间状态
     *
     * @param reservationStatus 房态
     * @param roomNumber        房间号
     *//*
    @Deprecated
    private void updateRoomStatus(String orgRoomNumber, String roomNumber, int reservationStatus, Date arrivalDate, Date deptDate) {
        boolean lnow = CalculateDate.isInRange(CalculateDate.getSystemDate(), arrivalDate, deptDate);   //产生对当前房间的占用.才会更新locc  提前分配10天后的房间.不会影响当前
        //预定中房间号为空，或者预定日期在未来日期的，则直接返回结束
        if (StrUtil.isEmpty(roomNumber) || !lnow) {
            return;
        }
        int locc = StatusTypeUtils.RoomLocc.OCCUPY;//0表示非占用，1表示占用
        String roomStatus = StatusTypeUtils.RoomStatus.DIRTY;//CL表示干净房，DI表示脏房
        //0->预订,对应房间状态为占用的，干净房
        if (reservationStatus == StatusTypeUtils.RsStatus.EXPECTED) {
            locc = StatusTypeUtils.RoomLocc.OCCUPY;
            roomStatus = StatusTypeUtils.RoomStatus.CLEAN;
        } else if (reservationStatus == StatusTypeUtils.RsStatus.CHECKIN) {
            locc = StatusTypeUtils.RoomLocc.OCCUPY;
            roomStatus = StatusTypeUtils.RoomStatus.DIRTY;
        } else if (reservationStatus == StatusTypeUtils.RsStatus.CHECKOUT) {
            locc = StatusTypeUtils.RoomLocc.UN_OCCUPY;
            roomStatus = StatusTypeUtils.RoomStatus.DIRTY;
        } else if (reservationStatus == StatusTypeUtils.RsStatus.CANCEL) {
            locc = StatusTypeUtils.RoomLocc.UN_OCCUPY;
            roomStatus = StatusTypeUtils.RoomStatus.CLEAN;
        } else if (reservationStatus == StatusTypeUtils.RsStatus.NOT_DEPARTED) {
            locc = StatusTypeUtils.RoomLocc.OCCUPY;
            roomStatus = StatusTypeUtils.RoomStatus.DIRTY;
        }
        roomMapper.updateRoomLoccAndDirty(locc, roomStatus, new Date(), GlobalContext.getCurrentUserId(), roomNumber, GlobalContext.getCurrentHotelId());
    }*/

    /**
     * 恢复取消. NOSHOW预订
     *
     * @param hotelId
     * @param orderNo
     * @param userId
     * @return
     * @throws DefinedException
     */
    public Reservation internalReactiveReservation(String hotelId, String orderNo, String userId) throws DefinedException {
        return coreRs.internalReactiveReservation(hotelId, orderNo, userId);
    }

    @Override
    public GroupRsListRes listReservationGroup(QueryStandardGroupReq queryStandardGroupReq) {
        int currentPage = queryStandardGroupReq.getPages().getQueryStartPage();
        int pageSize = queryStandardGroupReq.getPages().getPagesize();
        String hotelId = GlobalContext.getCurrentHotelId();

        //查询团队内所有待入住的订单

        List<String> needCheckInGroups = dao.getObjectList("select relationNumber from Reservation where hotelId=?1 and arrivalDate=?2 and reservationStatus=?3 group by relationNumber ",
                hotelId, CalculateDate.returnDate_ZeroTime(CalculateDate.getSystemDate()), StatusTypeUtils.RsStatus.EXPECTED);


        if (needCheckInGroups.size() == 0) {
            needCheckInGroups.add("XXX");
        }


        Map<String, Object> paramsMap = Maps.newHashMap();
        paramsMap.put("hotelId", hotelId);
        paramsMap.put("arrivalDate", CalculateDate.returnDate_ZeroTime(CalculateDate.getSystemDate()));
        paramsMap.put("needCheckInGroups", needCheckInGroups);

        String jpql = "from Colrs where  hotelId = :hotelId and arrivalDate = :arrivalDate  and bookingid in :needCheckInGroups ";
        if (StrUtil.isNotBlank(queryStandardGroupReq.getGroupname())) {
            jpql += " and groupname like :groupname ";
            paramsMap.put("groupname", "%" + queryStandardGroupReq.getGroupname() + "%");
        }
        if (StrUtil.isNotBlank(queryStandardGroupReq.getBooker())) {
            jpql += " and bookerName like :booker ";
            paramsMap.put("booker", "%" + queryStandardGroupReq.getBooker() + "%");
        }
        if (StrUtil.isNotBlank(queryStandardGroupReq.getTelephone())) {
            jpql += " and telephone = :telephone ";
            paramsMap.put("telephone", queryStandardGroupReq.getTelephone());
        }

        jpql += " order by groupname desc ";

        Page<Colrs> rsPage = dao.getListPage(jpql, paramsMap, queryStandardGroupReq.getPages().getPageable());


        GroupRsListRes res = new GroupRsListRes();
        res.fillPageData(rsPage);
        return res;
    }

    @Override
    public void transferGroup(GroupTransferReq req) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();
        String userId = GlobalContext.getCurrentUserId();

        for (String roomRsNo : req.getOpRsnos()) {
            // 查询要转移的预订
            Reservation reservation = findReservation(roomRsNo);
            String oldRelationNumber = reservation.getRelationNumber();

            // 如果原本就是散客,且目标也是散客,则无需操作
            if (StrUtil.isEmpty(oldRelationNumber) && StrUtil.isEmpty(req.getTargetRelationNumber())) {
                return;
            }

            // 如果是要加入团队,需要验证目标团队是否存在
            if (StrUtil.isNotEmpty(req.getTargetRelationNumber())) {
                List<Reservation> targetGroupRs = reservationMapper.findByRelationNumberAndHotelId(req.getTargetRelationNumber(), hotelId);
                if (CollUtil.isEmpty(targetGroupRs)) {
                    throw new BizException("目标团队不存在");
                }
            }

            // 如果是要离开团队,需要检查原团队是否至少保留一个订单
            if (StrUtil.isNotEmpty(oldRelationNumber)) {
                List<Reservation> originalGroupRs = reservationMapper.findByRelationNumberAndHotelId(oldRelationNumber, hotelId);
                if (originalGroupRs.size() <= 1) {
                    throw new BizException("团队至少需要保留一个订单");
                }
            }

            // 更新关联号
            reservation.setRelationNumber(req.getTargetRelationNumber());
            reservation.setModifiedBy(userId);
            reservation.setModifiedTime(new Date());
            reservationMapper.save(reservation);

            // 记录操作日志
            String content;
            if (StrUtil.isEmpty(req.getTargetRelationNumber())) {
                content = StrUtil.format("订单{}从团队{}出团", roomRsNo, oldRelationNumber);
            } else {
                content = StrUtil.format("订单{}{}团队{}",
                        roomRsNo,
                        StrUtil.isEmpty(oldRelationNumber) ? "加入" : "转入",
                        req.getTargetRelationNumber());
            }

            userLogService.writeLog(SystemUtil.UserLogType.COL,
                    SystemUtil.UserLogOpType.MODIFY,
                    userId,
                    roomRsNo,
                    content,
                    hotelId);
        }
    }

    @Override
    public RsOrderResult copyReservation(CopyReservationReq req) throws DefinedException {
       return coreRs.copyReservation(GlobalContext.getCurrentHotelId(), req.getReservationNumber());
    }

    @Override
    public void pendingCheckOut(PendingCheckOutReq pendingCheckOutReq) throws DefinedException {
        coreRs.pendingCheckOut(GlobalContext.getCurrentHotelId(), pendingCheckOutReq.getReservationNumber());
    }

    @Override
    public Reservation cancelPending(String hotelId, String orderNo, String userId) throws DefinedException {
        return coreRs.cancelPending(hotelId, orderNo, userId);
    }

    @Override
    public void cancelCheckout(CancelReq cancelReq) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();
        String userId = GlobalContext.getCurrentUserId();
        coreRs.cancelCheckout(hotelId, cancelReq.getReservationNumber(), userId);
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, userId,
                cancelReq.getReservationNumber(), "取消离店", hotelId);
    }

    @Override
    public CheckinVerificationRes checkinVerification(CheckinVerificationReq req) {
        return coreRs.checkinVerification(req);
    }
}
