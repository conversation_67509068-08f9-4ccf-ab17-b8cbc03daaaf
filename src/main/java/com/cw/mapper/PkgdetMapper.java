package com.cw.mapper;

import com.cw.entity.Pkgdet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 包价详情数据访问层
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Repository
public interface PkgdetMapper extends JpaRepository<Pkgdet, Long>, JpaSpecificationExecutor<Pkgdet> {

    /**
     * 检查是否存在重复的包价详情记录
     * 根据酒店ID、房价代码、房型代码和时间范围检查重复
     *
     * @param rateCode  房价代码
     * @param excludeId 排除的记录ID（用于修改时排除自身）
     * @param roomType  房型代码
     * @param hotelId   酒店ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 重复记录数量
     */
    @Query(value = "SELECT COUNT(*) FROM Pkgdet p WHERE p.rateCode = ?1 AND p.id != ?2 AND p.roomType = ?3 AND p.hotelId = ?4 " +
            "AND ((p.endTime BETWEEN ?5 AND ?6) OR (p.startTime <= ?5 AND p.endTime >= ?6))")
    int countByCodeAndIdNotAndRoomTypeAndHotelIdAndTimeOverlap(String rateCode, Long excludeId, String roomType,
                                                               String hotelId, Date startTime, Date endTime);

    /**
     * 根据房价代码、房型代码、酒店ID和时间范围查询包价详情列表
     *
     * @param rateCode  房价代码
     * @param roomType  房型代码
     * @param hotelId   酒店ID
     * @param calcStart 计算开始时间
     * @param calcEnd   计算结束时间
     * @return 包价详情列表
     */
    @Query(value = "FROM Pkgdet WHERE rateCode = ?1 AND roomType = ?2 AND hotelId = ?3 " +
            "AND ((endTime BETWEEN ?4 AND ?5) OR (startTime <= ?4 AND endTime >= ?5))")
    List<Pkgdet> getCalcPkgdetList(String rateCode, String roomType, String hotelId, Date calcStart, Date calcEnd);

    /**
     * 根据房价代码和酒店ID查询所有关联的房型代码
     *
     * @param rateCode 房价代码
     * @param hotelId  酒店ID
     * @return 房型代码列表
     */
    @Query(value = "SELECT DISTINCT p.roomType FROM Pkgdet p WHERE p.rateCode = ?1 AND p.hotelId = ?2")
    List<String> findDistinctRoomTypeByrateCodeAndHotelId(String rateCode, String hotelId);

    /**
     * 根据酒店ID、房价代码、房型代码查询包价详情列表
     *
     * @param hotelId  酒店ID
     * @param rateCode 房价代码
     * @param roomType 房型代码
     * @return 包价详情列表
     */
    List<Pkgdet> findByHotelIdAndRateCodeAndRoomType(String hotelId, String rateCode, String roomType);

    /**
     * 根据酒店ID和房价代码查询包价详情列表
     *
     * @param hotelId  酒店ID
     * @param rateCode 房价代码
     * @return 包价详情列表
     */
    List<Pkgdet> findByHotelIdAndRateCode(String hotelId, String rateCode);

    /**
     * 根据酒店ID查询包价详情列表
     *
     * @param hotelId 酒店ID
     * @return 包价详情列表
     */
    List<Pkgdet> findByHotelId(String hotelId);

    /**
     * 根据酒店ID、房价代码和房型代码精准查询包价详情列表
     * 使用FIND_IN_SET函数支持逗号分隔的房型字段精准匹配
     *
     * @param hotelId  酒店ID
     * @param rateCode 房价代码
     * @param roomType 房型代码（精准匹配）
     * @return 包价详情列表
     */
    @Query(value = "FROM Pkgdet WHERE hotelId = ?1 AND rateCode = ?2 AND FIND_IN_SET(?3, roomType) > 0")
    List<Pkgdet> findByHotelIdAndRateCodeAndRoomTypeExact(String hotelId, String rateCode, String roomType);

    /**
     * 根据酒店ID和房型代码精准查询包价详情列表
     * 使用FIND_IN_SET函数支持逗号分隔的房型字段精准匹配
     *
     * @param hotelId  酒店ID
     * @param roomType 房型代码（精准匹配）
     * @return 包价详情列表
     */
    @Query(value = "FROM Pkgdet WHERE hotelId = ?1 AND FIND_IN_SET(?2, roomType) > 0")
    List<Pkgdet> findByHotelIdAndRoomTypeExact(String hotelId, String roomType);
}
