package com.cw.service.config.print.simple;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import com.cw.entity.GuestAccounts;
import com.cw.entity.Reservation;
import com.cw.service.config.print.folio.FolioJasperGenerator;

import net.sf.jasperreports.engine.JasperExportManager;

public class DefaultFolioJasperGeneratorTest {
	public static void main(String[] args) throws Exception {
		Reservation reservation = new Reservation();
		reservation.setGuestName("XXX");
		List<GuestAccounts> accList = new ArrayList<>();
		for (int i = 0; i < 50; i++) {
			accList.add(createAcc());
		}
		String filePath = "templates/folio/003.jrxml";
		byte[] jrxmlBytes;
		try {
			jrxmlBytes = Files.readAllBytes(Paths.get(filePath));
		} catch (IOException e) {
			filePath = "templates/folio/default.jrxml";
			jrxmlBytes = Files.readAllBytes(Paths.get(filePath));
		}
		JasperExportManager.exportReportToPdfFile(new FolioJasperGenerator()//
				.setResv(reservation)//
				.setAccList(accList)//
				.generateReport(jrxmlBytes), "MultiLevelGroupingReport.pdf");
		System.out.println("报表已生成：MultiLevelGroupingReport.pdf");
	}

	private static GuestAccounts createAcc() {
		GuestAccounts acc = new GuestAccounts();
		acc.setBusiness_date(LocalDate.now());
		if ((int) (Math.random() * 100000) % 11 == 0) {
			acc.setCredit(BigDecimal.valueOf(Math.random() * 100000).setScale(2, RoundingMode.HALF_UP));
			acc.setDescription("结算");
		} else {
			acc.setPrice(BigDecimal.valueOf(Math.random() * 100000).setScale(2, RoundingMode.HALF_UP));
			acc.setQuantity(BigDecimal.ONE);
			acc.setDescription("夜审房费XY-B");
		}
		acc.setRoomNo("10178");
		return acc;
	}
}
